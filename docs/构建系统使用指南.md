# webcfg 优化构建系统使用指南

## 概述

经过优化的 webcfg 构建系统实现了以下主要功能：

✅ **第三方库缓存机制** - 避免重复编译，大幅提升构建速度  
✅ **多平台支持** - 支持 native、am335x、zynq、2440、ec20 等平台  
✅ **静态库编译** - 所有第三方库编译为静态库，减少部署依赖  
✅ **目录结构优化** - 清晰的源码、构建、缓存、安装目录分离  
✅ **混合构建系统** - 主项目使用cmake，第三方库使用各自原生构建系统  
✅ **自动化脚本** - 完整的构建和部署自动化流程

## 目录结构

```
webcfg_small/
├── build/                      # 构建输出目录
│   ├── native/                 # 本地平台构建
│   ├── am335x/                 # AM335x平台构建
│   └── ...                     # 其他平台构建
├── cache/                      # 第三方库缓存目录 (新增)
│   └── third_party/
│       ├── native/
│       │   ├── cjson/          # cjson库缓存
│       │   │   ├── lib/        # 静态库文件
│       │   │   └── include/    # 头文件
│       │   ├── microhttpd/     # microhttpd库缓存
│       │   │   ├── lib/        # 静态库文件
│       │   │   └── include/    # 头文件
│       │   └── cgic/           # cgic库缓存
│       ├── am335x/
│       └── ...
├── install/                    # 安装目录
│   ├── common/                 # 平台无关资源
│   │   ├── include/            # 头文件
│   │   └── web/                # Web资源
│   └── platform/               # 平台特定资源
│       ├── native/
│       │   ├── bin/            # 可执行文件
│       │   └── lib/            # 库文件
│       └── ...
├── third_party/                # 第三方库源码 (仅源码)
│   ├── cjson/
│   ├── microhttpd/
│   └── cgic/
├── cmake/                      # CMake配置文件
│   ├── ThirdPartyManager.cmake # 第三方库管理器
│   ├── InstallConfig.cmake     # 安装配置
│   └── toolchains/             # 工具链配置
└── scripts/                    # 构建脚本
    └── build/
        ├── build.sh            # 优化构建脚本
        └── build_all_platforms.sh # 批量构建脚本
```

## 快速开始

### 1. 环境准备

#### 1.1 必需工具
```bash
# Ubuntu/Debian系统
sudo apt-get install \
    cmake \
    build-essential \
    autotools-dev \
    autoconf \
    automake \
    libtool

# CentOS/RHEL系统
sudo yum install \
    cmake \
    gcc \
    gcc-c++ \
    make \
    autoconf \
    automake \
    libtool
```

#### 1.2 工具版本要求
- **cmake**: >= 3.12
- **gcc**: >= 4.8
- **autoconf**: >= 2.69
- **automake**: >= 1.15
- **libtool**: >= 2.4

### 2. 单平台构建

```bash
# 构建本地平台
./scripts/build/build.sh native

# 构建ARM平台
./scripts/build/build.sh am335x

# Debug模式构建
./scripts/build/build.sh native Debug

# 清理并重建（包括第三方库）
./scripts/build/build.sh native Release 1 1
```

### 3. 批量构建所有平台

```bash
# 构建所有支持的平台
./scripts/build/build_all_platforms.sh

# Debug模式构建所有平台
./scripts/build/build_all_platforms.sh Debug

# 清理并重建所有平台
./scripts/build/build_all_platforms.sh Release 1
```

## 详细使用说明

### 构建脚本参数

#### build.sh
```bash
./scripts/build/build.sh [平台] [构建类型] [清理] [重建第三方库] [并行任务数]
```

**参数说明：**
- `平台`: native|am335x|zynq|2440|ec20 (默认: native)
- `构建类型`: Debug|Release (默认: Release)
- `清理`: 0|1 是否清理构建目录 (默认: 0)
- `重建第三方库`: 0|1 是否强制重建第三方库 (默认: 0)
- `并行任务数`: 编译并行任务数 (默认: CPU核心数)

#### build_all_platforms.sh
```bash
./scripts/build/build_all_platforms.sh [构建类型] [清理] [重建第三方库]
```

### 环境变量配置

可以通过环境变量自定义目录路径：

```bash
export WEBCFG_BUILD_ROOT="/custom/build"      # 构建根目录
export WEBCFG_CACHE_ROOT="/custom/cache"      # 缓存根目录  
export WEBCFG_INSTALL_ROOT="/custom/install"  # 安装根目录
```

## 第三方库混合构建系统

### 构建系统类型

| 库名 | 构建系统 | 说明 |
|------|----------|------|
| cjson | cmake | 使用cmake配置和构建 |
| microhttpd | autotools | 使用configure、make构建 |
| cgic | makefile | 使用简单make构建 |

### 缓存策略

1. **自动缓存**: 第三方库首次编译后自动缓存到 `cache/third_party/${PLATFORM}/`
2. **智能检测**: 检查静态库文件是否存在来判断缓存状态
3. **平台隔离**: 每个平台有独立的缓存目录
4. **增量构建**: 只有缓存不存在时才重新编译

### 构建流程

```bash
# 1. 检查依赖工具
check_dependencies()
  ├── cmake (主项目)
  ├── autoconf, automake, libtool (microhttpd)
  └── gcc (编译器)

# 2. 检查第三方库缓存
check_third_party_cache()
  ├── cjson: cache/third_party/${PLATFORM}/cjson/lib/libcjson.a
  ├── microhttpd: cache/third_party/${PLATFORM}/microhttpd/lib/libmicrohttpd.a
  └── cgic: cache/third_party/${PLATFORM}/cgic/lib/libcgic.a

# 3. 构建第三方库（如果缓存不存在）
build_third_party()
  ├── build_cjson()       # cmake构建
  ├── build_microhttpd()  # autotools构建
  └── build_cgic()        # makefile构建

# 4. 构建主项目
build_main_project()     # cmake构建

# 5. 安装
install_project()
```

### 缓存管理

```bash
# 查看缓存状态
find cache/ -name "*.a" -exec ls -lh {} \;

# 查看第三方库状态
./scripts/build/build.sh native Release 0 0 | grep "第三方库状态"

# 清理特定平台缓存
rm -rf cache/third_party/am335x

# 清理所有缓存
rm -rf cache/

# 强制重建第三方库
./scripts/build/build.sh native Release 0 1
```

## 工具链配置

### 支持的平台

| 平台 | 说明 | 工具链文件 |
|------|------|------------|
| native | 本地x86_64平台 | - |
| am335x | AM335x ARM Cortex-A8 | cmake/toolchains/am335x.cmake |
| zynq | Xilinx Zynq ARM | cmake/toolchains/zynq.cmake |
| 2440 | Samsung S3C2440 ARM9 | cmake/toolchains/2440.cmake |
| ec20 | Quectel EC20 4G模块 | cmake/toolchains/ec20.cmake |

### 添加新平台

1. 创建工具链文件 `cmake/toolchains/新平台.cmake`
2. 在构建脚本中添加平台名称到支持列表
3. 确保交叉编译器已正确安装
4. 测试第三方库的交叉编译支持

示例工具链文件：
```cmake
# cmake/toolchains/新平台.cmake
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR arm)

set(CROSS_COMPILE_PREFIX "/path/to/toolchain/bin/arm-linux-")
set(CMAKE_C_COMPILER ${CROSS_COMPILE_PREFIX}gcc)
set(CMAKE_CXX_COMPILER ${CROSS_COMPILE_PREFIX}g++)

set(CMAKE_FIND_ROOT_PATH "/path/to/sysroot")
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
```

## 常见问题解决

### 1. 构建失败

**问题**: 编译出错或配置失败
**解决方案**:
```bash
# 检查依赖工具
cmake --version
gcc --version
autoconf --version
automake --version
libtool --version

# 清理并重新构建
./scripts/build/build.sh native Release 1 1

# 查看详细错误日志
./scripts/build/build.sh native Debug
```

### 2. 第三方库问题

**问题**: 第三方库编译失败
**解决方案**:
```bash
# 检查第三方库源码完整性
ls -la third_party/*/

# 强制重建第三方库
./scripts/build/build.sh native Release 0 1

# 手动清理缓存
rm -rf cache/third_party/

# 检查autotools工具
sudo apt-get install autotools-dev autoconf automake libtool build-essential
```

### 3. microhttpd特定问题

**问题**: microhttpd autotools构建失败
**解决方案**:
```bash
# 检查autogen.sh脚本
ls -la third_party/microhttpd/autogen.sh

# 手动运行autogen.sh
cd third_party/microhttpd
chmod +x autogen.sh
./autogen.sh

# 检查configure脚本是否生成
ls -la third_party/microhttpd/configure

# 验证autotools版本
autoconf --version  # >= 2.69
automake --version  # >= 1.15
libtool --version   # >= 2.4
```

### 4. 交叉编译问题

**问题**: 交叉编译环境配置错误
**解决方案**:
```bash
# 检查工具链文件
cat cmake/toolchains/am335x.cmake

# 验证编译器路径
/disk/platform/am335x/sdk/bin/arm-linux-gnueabihf-gcc --version

# 检查系统根目录
ls /disk/platform/am335x/sdk/arm-linux-gnueabihf/libc/

# 测试交叉编译环境
export CC=/disk/platform/am335x/sdk/bin/arm-linux-gnueabihf-gcc
echo 'int main(){return 0;}' | $CC -x c - -o test_cross
file test_cross
```

### 5. 权限问题

**问题**: 没有写入权限
**解决方案**:
```bash
# 检查目录权限
ls -ld build/ cache/ install/

# 修改权限
chmod -R 755 build/ cache/ install/

# 使用自定义目录
export WEBCFG_BUILD_ROOT="/tmp/webcfg_build"
export WEBCFG_CACHE_ROOT="/tmp/webcfg_cache"
```

## 性能优化

### 构建时间优化

1. **使用缓存**: 第三方库缓存可节省 70-80% 构建时间
2. **并行编译**: 根据CPU核心数调整并行任务数
3. **增量构建**: 只编译修改的源文件
4. **分离构建**: 第三方库与主项目分离构建

### 磁盘空间优化

1. **缓存清理**: 定期清理不需要的平台缓存
2. **构建目录**: 构建完成后可以删除 `build/` 目录
3. **压缩安装包**: 使用 tar.gz 压缩安装包

```bash
# 创建发布包
tar -czf webcfg-${VERSION}-all-platforms.tar.gz install/

# 只保留特定平台
tar -czf webcfg-${VERSION}-am335x.tar.gz install/common/ install/platform/am335x/
```

## 高级功能

### 1. 自定义CMake选项

```bash
# 编译时添加自定义选项
cmake -DCUSTOM_OPTION=ON \
      -DTARGET_PLATFORM=am335x \
      -DCMAKE_BUILD_TYPE=Release \
      /path/to/source
```

### 2. 调试构建

```bash
# 启用详细输出
./scripts/build/build.sh native Debug

# 查看编译命令
make VERBOSE=1

# 使用gdb调试
gdb ./build/native/webcfg-server
```

### 3. 持续集成

```bash
# CI/CD 脚本示例
#!/bin/bash
set -e

# 安装依赖
sudo apt-get update
sudo apt-get install -y cmake build-essential autotools-dev autoconf automake libtool

# 构建所有平台
./scripts/build/build_all_platforms.sh Release

# 运行测试
./install/platform/native/bin/webcfg-server --test

# 创建发布包
tar -czf webcfg-release.tar.gz install/
```

## 总结

优化后的构建系统具有以下优势：

- 🚀 **高效**: 第三方库缓存机制大幅提升构建速度
- 🔧 **灵活**: 支持多种平台和构建配置
- 📁 **整洁**: 清晰的目录结构和文件组织
- 🤖 **自动化**: 完整的自动化构建和部署流程
- 🛠️ **可维护**: 模块化设计便于维护和扩展
- 🔄 **混合构建**: 各库使用最适合的原生构建系统

通过这个优化方案，您可以高效地进行多平台开发和部署，同时保持代码仓库的整洁性。第三方库使用各自原生的构建系统确保了最佳的兼容性和稳定性。 