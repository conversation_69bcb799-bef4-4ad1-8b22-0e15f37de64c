# 重构方案设计文档

## 1. 重构目标

### 1.1 主要目标
- **100%功能兼容**: 保持所有现有功能不变
- **前后端分离**: 解耦前后端，提供REST API接口
- **现代化界面**: 使用现代Web技术重构用户界面
- **构建系统升级**: 使用cmake替代Makefile构建系统
- **多平台支持**: 保持对所有现有硬件平台的支持
- **混合构建系统**: 主项目使用cmake，第三方库使用各自原生构建系统

### 1.2 约束条件
- 不修改第三方库源码(cgic、cJSON、microhttpd等)
- 保持配置文件格式完全兼容
- 保持业务逻辑不变
- 支持中英文界面切换
- 尊重第三方库的原生构建系统

## 2. 总体架构设计

### 2.1 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 (Web Frontend)                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   HTML5 Pages   │ │   CSS3 Styles   │ │   JavaScript    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │ HTTP/JSON
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                  后端 API服务 (Backend API)                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │   API Gateway   │ │  Config Handler │ │  Auth Handler   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                 业务逻辑层 (Business Logic)                  │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Network Config  │ │  Device Config  │ │  System Config  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                               │
                               ▼
┌─────────────────────────────────────────────────────────────┐
│                 数据访问层 (Data Access)                     │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  Binary Config  │ │   INI Config    │ │  System Files   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 模块分层设计

#### 2.2.1 前端层 (Presentation Layer)
- **技术栈**: HTML5 + CSS3 + JavaScript
- **布局**: 响应式布局替代frameset
- **交互**: 基于fetch API的异步通信
- **样式**: 现代化UI设计，保持功能区域布局

#### 2.2.2 API服务层 (API Service Layer)
- **技术栈**: C语言 + libmicrohttpd HTTP服务器
- **协议**: RESTful API + JSON数据格式
- **认证**: 基于token的身份验证
- **路由**: 统一的API路由管理

#### 2.2.3 业务逻辑层 (Business Logic Layer)
- **复用策略**: 最大化复用现有业务逻辑代码
- **重构范围**: 仅重构接口层，保持核心逻辑不变
- **模块化**: 按功能模块重新组织代码结构

#### 2.2.4 数据访问层 (Data Access Layer)
- **兼容性**: 完全保持现有配置文件格式
- **封装**: 提供统一的配置读写接口
- **事务性**: 保证配置操作的原子性

## 3. 技术选型方案

### 3.1 后端技术选型

#### 3.1.1 HTTP服务器
**选择方案**: libmicrohttpd
- 轻量级、高性能
- 支持多线程
- API简洁易用
- 使用autotools原生构建系统
- 静态库编译，减少依赖

#### 3.1.2 JSON处理
**选择方案**: cJSON库
- 轻量级JSON解析库
- C语言原生支持
- API简单直观
- 内存占用小
- 使用cmake原生构建系统

#### 3.1.3 API框架设计
```c
// API路由结构设计
typedef struct {
    char *method;           // HTTP方法 (GET/POST/PUT/DELETE)
    char *path;            // API路径
    int (*handler)(struct MHD_Connection *, const char *, cJSON *);
    int auth_required;     // 是否需要认证
} api_route_t;

// 统一的API响应格式
typedef struct {
    int code;              // 状态码
    char *message;         // 消息
    cJSON *data;          // 数据
} api_response_t;
```

### 3.2 前端技术选型

#### 3.2.1 页面布局
**HTML5结构设计**:
```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VICTEL IP交换机配置系统</title>
</head>
<body>
    <header class="app-header">
        <!-- 顶部导航 -->
    </header>
    <div class="app-container">
        <aside class="app-sidebar">
            <!-- 侧边栏菜单 -->
        </aside>
        <main class="app-main">
            <!-- 主要内容区域 -->
        </main>
    </div>
</body>
</html>
```

#### 3.2.2 CSS框架
**自定义CSS框架**:
- 基于CSS Grid和Flexbox布局
- 响应式设计支持
- 保持原有色彩主题
- 提升视觉体验

#### 3.2.3 JavaScript架构
**模块化JavaScript设计**:
```javascript
// 主应用模块
const App = {
    config: {},
    api: {},
    ui: {},
    utils: {},
    init: function() {
        // 应用初始化
    }
};

// API通信模块
const API = {
    baseURL: '/api/v1',
    request: async function(method, url, data) {
        // 统一API请求处理
    },
    config: {
        get: () => API.request('GET', '/config'),
        save: (data) => API.request('POST', '/config', data)
    }
};
```

### 3.3 混合构建系统

#### 3.3.1 主项目CMake配置
```cmake
cmake_minimum_required(VERSION 3.12)
project(webcfg_system)

# 平台检测
if(CMAKE_SYSTEM_PROCESSOR MATCHES "arm")
    if(DEFINED ENV{CROSS_COMPILE})
        set(CMAKE_C_COMPILER $ENV{CROSS_COMPILE}gcc)
        set(CMAKE_CXX_COMPILER $ENV{CROSS_COMPILE}g++)
    endif()
endif()

# 编译选项
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_FLAGS "-Wall -O2")

# 查找预构建的第三方库
find_library(CJSON_LIB cjson PATHS ${WEBCFG_CACHE_DIR}/cjson/lib)
find_library(MICROHTTPD_LIB microhttpd PATHS ${WEBCFG_CACHE_DIR}/microhttpd/lib)
find_library(CGIC_LIB cgic PATHS ${WEBCFG_CACHE_DIR}/cgic/lib)

# 子目录
add_subdirectory(src)
add_subdirectory(web)
```

#### 3.3.2 第三方库构建策略
```bash
# 不同库使用不同构建系统
build_cjson() {
    # 使用cmake构建
    cmake -DBUILD_SHARED_LIBS=OFF -DENABLE_CJSON_TEST=OFF
    make && make install
}

build_microhttpd() {
    # 使用autotools构建
    ./autogen.sh
    ./configure --enable-static --disable-shared --disable-https
    make && make install
}

build_cgic() {
    # 使用makefile构建
    make && cp libcgic.a $install_dir/lib/
}
```

## 4. API接口设计

### 4.1 RESTful API规范

#### 4.1.1 URL设计规范
```
基础URL: http://device-ip/api/v1

配置相关:
GET    /api/v1/config/network        - 获取网络配置
POST   /api/v1/config/network        - 保存网络配置
GET    /api/v1/config/device/{type}  - 获取设备配置
POST   /api/v1/config/device/{type}  - 保存设备配置

系统相关:
GET    /api/v1/system/info          - 获取系统信息
POST   /api/v1/system/reboot        - 重启系统
GET    /api/v1/system/logs          - 获取系统日志

认证相关:
POST   /api/v1/auth/login           - 用户登录
POST   /api/v1/auth/logout          - 用户退出
GET    /api/v1/auth/status          - 认证状态
```

#### 4.1.2 请求响应格式
```json
// 统一请求格式
{
    "action": "save",
    "data": {
        // 具体数据
    }
}

// 统一响应格式
{
    "code": 200,
    "message": "success",
    "data": {
        // 响应数据
    },
    "timestamp": 1635123456
}

// 错误响应格式
{
    "code": 400,
    "message": "参数错误",
    "error": "Invalid IP address format",
    "timestamp": 1635123456
}
```

### 4.2 核心API设计

#### 4.2.1 网络配置API
```c
// 网络配置结构体
typedef struct {
    char ip[16];          // IP地址字符串
    char mask[16];        // 子网掩码
    char gateway[16];     // 网关
    char dns[16];        // DNS服务器
    char mac[18];        // MAC地址
} network_config_t;

// API处理函数
int handle_network_get(struct MHD_Connection *connection, 
                      const char *url, cJSON *request_data);
int handle_network_post(struct MHD_Connection *connection, 
                       const char *url, cJSON *request_data);
```

#### 4.2.2 设备配置API
```c
// 设备配置通用结构
typedef struct {
    char device_type[32]; // 设备类型
    cJSON *config_data;   // 配置数据JSON
} device_config_t;

// API处理函数
int handle_device_get(struct MHD_Connection *connection, 
                     const char *url, cJSON *request_data);
int handle_device_post(struct MHD_Connection *connection, 
                      const char *url, cJSON *request_data);
```

## 5. 数据库设计

### 5.1 配置存储策略
- **保持兼容**: 继续使用现有二进制和ini配置文件格式
- **添加缓存**: 内存中维护配置数据缓存
- **事务支持**: 实现配置修改的回滚机制

### 5.2 配置映射设计
```c
// 配置映射表
typedef struct {
    char *api_path;           // API路径
    char *config_file;        // 配置文件路径
    size_t config_size;       // 配置大小
    int (*validator)(void *); // 数据验证函数
    int (*converter)(cJSON *, void *); // JSON转换函数
} config_mapping_t;

// 配置映射表实例
static config_mapping_t config_mappings[] = {
    {"/config/network", "/etc/eth0-setting", sizeof(stCfgNet), 
     validate_network, convert_network_json},
    {"/config/device/center", CALLCENTERCFG, sizeof(stCfgCenter), 
     validate_center, convert_center_json},
    // ... 其他配置映射
};
```

## 6. 安全设计

### 6.1 认证机制
```c
// Token结构设计
typedef struct {
    char token[64];      // 认证token
    time_t expires;      // 过期时间
    char username[32];   // 用户名
    int privileges;      // 权限级别
} auth_token_t;

// 认证中间件
int auth_middleware(struct MHD_Connection *connection, 
                   const char *url, cJSON *request_data);
```

### 6.2 输入验证
```c
// 输入验证函数
int validate_ip_address(const char *ip);
int validate_mac_address(const char *mac);
int validate_port_number(int port);
int validate_config_data(const char *config_type, cJSON *data);
```

### 6.3 访问控制
- 基于角色的访问控制(RBAC)
- 敏感操作需要管理员权限
- 操作日志记录

## 7. 性能优化

### 7.1 后端优化
- 配置数据缓存机制
- 异步I/O处理
- 连接池管理
- 内存使用优化

### 7.2 前端优化
- 资源压缩与合并
- 缓存策略
- 按需加载
- 响应式图片

## 8. 兼容性保证

### 8.1 配置文件兼容
- 完全保持二进制配置文件或ini配置文件格式
- 提供配置文件版本检查
- 支持配置迁移工具

### 8.2 业务逻辑兼容
- 保持所有现有业务规则
- 数据验证逻辑不变
- 错误处理机制一致

### 8.3 多平台兼容
- 交叉编译支持
- 平台特定优化
- 硬件适配层抽象

### 8.4 构建系统兼容
- 主项目使用cmake现代构建系统
- 第三方库保持原生构建系统
- 混合构建流程自动化

## 9. 测试策略

### 9.1 单元测试
- 配置读写功能测试
- API接口测试
- 数据验证测试

### 9.2 集成测试
- 前后端集成测试
- 多平台编译测试
- 配置文件兼容性测试
- 第三方库集成测试

### 9.3 系统测试
- 功能完整性测试
- 性能压力测试
- 安全性测试

## 10. 部署方案

### 10.1 开发环境
- 自动化构建流水线
- 代码质量检查
- 混合构建系统支持

### 10.2 生产部署
- 一键部署脚本
- 配置备份恢复
- 服务监控机制

### 10.3 升级策略
- 平滑升级机制
- 配置数据迁移
- 回滚方案设计

## 11. 项目里程碑

### 第一阶段 (架构搭建) ✅ 已完成
- [x] 项目目录结构创建
- [x] 混合构建系统搭建
- [x] 基础HTTP服务器集成（占位符）
- [x] API框架设计实现

### 第二阶段 (核心功能)
- [ ] libmicrohttpd完整集成
- [ ] 配置读写API实现
- [ ] 前端界面重构
- [ ] 认证授权机制
- [ ] 基础功能测试

### 第三阶段 (功能完善)
- [ ] 所有功能模块实现
- [ ] 多平台适配
- [ ] 性能优化
- [ ] 全面测试

### 第四阶段 (部署上线)
- [ ] 生产环境部署
- [ ] 用户验收测试
- [ ] 文档完善
- [ ] 项目交付

这个重构方案在保持100%功能兼容的前提下，实现了前后端分离和现代化改造，同时采用混合构建系统确保各组件使用最适合的构建方式，为项目的长期维护和发展奠定了良好基础。 