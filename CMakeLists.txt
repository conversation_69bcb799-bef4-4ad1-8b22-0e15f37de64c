cmake_minimum_required(VERSION 3.16)

# 项目信息
project(webcfg_modular
    VERSION 1.0.0
    DESCRIPTION "WebCfg system with modular third-party library management"
    LANGUAGES C
)

# 设置C标准
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# 输出详细信息
message(STATUS "=== WebCfg 模块化构建系统 ===")
message(STATUS "项目名称: ${PROJECT_NAME}")
message(STATUS "项目版本: ${PROJECT_VERSION}")
message(STATUS "构建类型: ${CMAKE_BUILD_TYPE}")
message(STATUS "目标平台: ${TARGET_PLATFORM}")

# 设置必要的路径变量
if(NOT DEFINED WEBCFG_ROOT_DIR)
    set(WEBCFG_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
endif()

if(NOT DEFINED WEBCFG_CACHE_DIR)
    set(WEBCFG_CACHE_DIR ${WEBCFG_ROOT_DIR}/cache)
endif()

if(NOT DEFINED WEBCFG_INSTALL_DIR)
    set(WEBCFG_INSTALL_DIR ${CMAKE_INSTALL_PREFIX})
endif()

message(STATUS "项目根目录: ${WEBCFG_ROOT_DIR}")
message(STATUS "缓存目录: ${WEBCFG_CACHE_DIR}")
message(STATUS "安装目录: ${WEBCFG_INSTALL_DIR}")

# 创建缓存目录
file(MAKE_DIRECTORY ${WEBCFG_CACHE_DIR})

# 引入模块化第三方库管理系统
include(cmake/ThirdPartyManager.cmake)

# 设置第三方库列表
set(THIRD_PARTY_LIBS cjson microhttpd cgic)

# 处理命令行选项
if(SHOW_THIRD_PARTY_CONFIG)
    show_third_party_config()
    return()
endif()

if(SHOW_THIRD_PARTY_INFO)
    show_third_party_info()
    return()
endif()

if(CLEAN_THIRD_PARTY_CACHE)
    clean_third_party_cache()
    return()
endif()

# 处理第三方库
handle_third_party_libraries()

# 设置包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# 添加第三方库头文件目录
foreach(LIB_NAME ${THIRD_PARTY_LIBS})
    # WEBCFG_CACHE_DIR已经包含了third_party/platform路径
    set(LIB_INCLUDE_DIR "${WEBCFG_CACHE_DIR}/${LIB_NAME}/include")
    if(EXISTS ${LIB_INCLUDE_DIR})
        # 对于cjson，需要添加子目录路径
        if(${LIB_NAME} STREQUAL "cjson")
            set(CJSON_INCLUDE_DIR "${LIB_INCLUDE_DIR}/cjson")
            if(EXISTS ${CJSON_INCLUDE_DIR})
                include_directories(${CJSON_INCLUDE_DIR})
                message(STATUS "添加cJSON头文件目录: ${CJSON_INCLUDE_DIR}")
            endif()
        else()
            include_directories(${LIB_INCLUDE_DIR})
            message(STATUS "添加第三方库头文件目录: ${LIB_INCLUDE_DIR}")
        endif()
    else()
        message(WARNING "第三方库头文件目录不存在: ${LIB_INCLUDE_DIR}")
    endif()
endforeach()

# 收集源文件
file(GLOB_RECURSE WEBCFG_SOURCES
    "src/*.c"
    "src/*.h"
)

# 创建可执行文件
add_executable(webcfg ${WEBCFG_SOURCES})

# 链接第三方库
# 使用现代CMake目标链接方式
target_link_libraries(webcfg PRIVATE
    cjson
    microhttpd
    cgic
)

# 添加系统库（如果需要）
if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    target_link_libraries(webcfg PRIVATE
        pthread
        m
        dl
    )
endif()

# 设置编译选项
target_compile_options(webcfg PRIVATE
    -Wall
    -Wextra
    -Werror
    -Wno-unused-parameter  # 第一阶段占位符实现，允许未使用参数
    $<$<CONFIG:Debug>:-g -O0>
    $<$<CONFIG:Release>:-O2 -DNDEBUG>
)

# 设置预处理器定义
target_compile_definitions(webcfg PRIVATE
    WEBCFG_VERSION="${PROJECT_VERSION}"
    TARGET_PLATFORM="${TARGET_PLATFORM}"
    $<$<CONFIG:Debug>:DEBUG>
    $<$<CONFIG:Release>:NDEBUG>
)

# 安装配置
include(cmake/InstallConfig.cmake)

# 安装配置文件
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/etc)
    install(DIRECTORY etc/
        DESTINATION etc/webcfg
        COMPONENT configuration
        FILES_MATCHING PATTERN "*.conf" PATTERN "*.json"
    )
endif()

# 安装文档
if(EXISTS ${CMAKE_CURRENT_SOURCE_DIR}/doc)
    install(DIRECTORY doc/
        DESTINATION share/doc/webcfg
        COMPONENT documentation
        FILES_MATCHING PATTERN "*.md" PATTERN "*.txt"
    )
endif()

# 显示构建摘要
message(STATUS "=== 构建配置摘要 ===")
message(STATUS "可执行文件: webcfg")
message(STATUS "第三方库: cjson, microhttpd, cgic")
message(STATUS "安装前缀: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "========================")

# 添加自定义目标
add_custom_target(show-config
    COMMAND ${CMAKE_COMMAND} -E echo "=== 第三方库配置 ==="
    COMMAND ${CMAKE_COMMAND} -DSHOW_THIRD_PARTY_CONFIG=ON -P ${CMAKE_CURRENT_SOURCE_DIR}/cmake/ThirdPartyManager.cmake
    VERBATIM
)

add_custom_target(show-info
    COMMAND ${CMAKE_COMMAND} -E echo "=== 第三方库信息 ==="
    COMMAND ${CMAKE_COMMAND} -DSHOW_THIRD_PARTY_INFO=ON -P ${CMAKE_CURRENT_SOURCE_DIR}/cmake/ThirdPartyManager.cmake
    VERBATIM
)

add_custom_target(clean-cache
    COMMAND ${CMAKE_COMMAND} -E echo "=== 清理第三方库缓存 ==="
    COMMAND ${CMAKE_COMMAND} -DCLEAN_THIRD_PARTY_CACHE=ON -P ${CMAKE_CURRENT_SOURCE_DIR}/cmake/ThirdPartyManager.cmake
    VERBATIM
) 