# 2440交叉编译工具链配置
set(CMAKE_SYSTEM_NAME Linux)
set(CMAKE_SYSTEM_PROCESSOR arm)

# 交叉编译器路径
set(CROSS_COMPILE_PREFIX "/disk/platform/2440/sdk/bin/arm-linux-")
set(CMAKE_C_COMPILER ${CROSS_COMPILE_PREFIX}gcc)
set(CMAKE_CXX_COMPILER ${CROSS_COMPILE_PREFIX}g++)
set(CMAKE_ASM_COMPILER ${CROSS_COMPILE_PREFIX}gcc)

# 工具链工具
set(CMAKE_AR ${CROSS_COMPILE_PREFIX}ar)
set(CMAKE_RANLIB ${CROSS_COMPILE_PREFIX}ranlib)
set(CMAKE_STRIP ${CROSS_COMPILE_PREFIX}strip)

# 系统根目录
set(CMAKE_SYSROOT "/disk/platform/2440/sdk/arm-none-linux-gnueabi/sys-root")
set(CMAKE_FIND_ROOT_PATH ${CMAKE_SYSROOT})

# 搜索路径配置
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# 平台特定编译选项
set(CMAKE_C_FLAGS_INIT "-mcpu=arm920t")
set(CMAKE_CXX_FLAGS_INIT "-mcpu=arm920t")

# 链接器选项
set(CMAKE_EXE_LINKER_FLAGS_INIT "-Wl,--gc-sections")
set(CMAKE_SHARED_LINKER_FLAGS_INIT "-Wl,--gc-sections")

# 平台标识
add_definitions(-DTARGET_PLATFORM_2440=1) 