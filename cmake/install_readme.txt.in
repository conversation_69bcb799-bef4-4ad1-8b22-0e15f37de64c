# WebCfg 嵌入式网页配置系统

## 安装信息

- **版本**: @PROJECT_VERSION@
- **目标平台**: @TARGET_PLATFORM@
- **编译时间**: @CMAKE_BUILD_DATE@
- **安装目录**: @CMAKE_INSTALL_PREFIX@

## 目录结构

```
@CMAKE_INSTALL_PREFIX@/
├── common/                     # 平台无关资源
│   ├── include/               # 头文件
│   └── web/                   # Web资源文件
└── platform/@TARGET_PLATFORM@/   # 平台特定资源
    ├── bin/                   # 可执行文件
    │   ├── webcfg             # 主程序
    │   └── start_server.sh    # 启动脚本
    └── lib/                   # 库文件
```

## 快速开始

### 1. 启动服务器

```bash
# 使用默认配置启动
./platform/@TARGET_PLATFORM@/bin/start_server.sh

# 指定端口启动
./platform/@TARGET_PLATFORM@/bin/start_server.sh -p 8080

# 以守护进程模式启动
./platform/@TARGET_PLATFORM@/bin/start_server.sh -d
```

### 2. 直接启动主程序

```bash
# 进入安装目录
cd @CMAKE_INSTALL_PREFIX@

# 启动主程序
./platform/@TARGET_PLATFORM@/bin/webcfg -p 80 -w ./common/web
```

### 3. 访问Web界面

在浏览器中访问: `http://设备IP地址:端口`

默认端口: 80

## 配置说明

### 命令行参数

- `-p, --port PORT`: 设置监听端口 (默认: 80)
- `-w, --web-root DIR`: 设置Web资源目录
- `-h, --help`: 显示帮助信息

### 配置文件

配置文件位于系统目录中，通过Web界面进行管理：
- 网络配置: `/etc/eth0-setting`
- 设备配置: `/home/<USER>/cfg/` 目录下的各配置文件

## 系统要求

- Linux 操作系统
- 最小内存: 32MB
- 磁盘空间: 10MB

## 技术支持

如有问题，请查看系统日志或联系技术支持。

---
WebCfg 版本 @PROJECT_VERSION@ - 构建于 @CMAKE_BUILD_DATE@ 