<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>呼叫中心配置 - VICTEL IP交换机</title>
    <style>
        :root {
            --primary-color: #00A6A6;
            --secondary-color: #993333;
            --background-color: #f5f5f5;
            --text-color: #333;
            --border-color: #ddd;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 13px;
            color: var(--text-color);
            background-color: var(--background-color);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            margin-bottom: 2rem;
            border-radius: 5px;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 14px;
            opacity: 0.9;
        }

        .config-form {
            background: white;
            padding: 2rem;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-section h3 {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: var(--text-color);
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 3px;
            font-size: 13px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-group .help-text {
            font-size: 11px;
            color: #666;
            margin-top: 0.25rem;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
            transition: background-color 0.3s ease;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #008a8a;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background-color: #772222;
        }

        .status-message {
            padding: 12px;
            margin-bottom: 1rem;
            border-radius: 3px;
            display: none;
        }

        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid var(--border-color);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>呼叫中心配置</h1>
            <p>配置呼叫中心系统的基本参数和网络设置</p>
        </div>

        <div id="status-message" class="status-message"></div>
        <div id="loading" class="loading">正在加载配置...</div>

        <form id="center-config-form" class="config-form">
            <div class="form-section">
                <h3>基本配置</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="center_no">中心号码</label>
                        <input type="number" id="center_no" name="center_no" min="1" max="16777215" required>
                        <div class="help-text">24位有效，取值范围：1-16777215</div>
                    </div>
                    <div class="form-group">
                        <label for="center_outssi">外部SSI</label>
                        <input type="number" id="center_outssi" name="center_outssi" min="1" max="16777215" required>
                        <div class="help-text">中心台统一外部号码</div>
                    </div>
                    <div class="form-group">
                        <label for="center_inssi">内部SSI</label>
                        <input type="number" id="center_inssi" name="center_inssi" min="1" max="16777215" required>
                        <div class="help-text">中心台统一内应号码</div>
                    </div>
                    <div class="form-group">
                        <label for="vchan_sum">语音通道数</label>
                        <input type="number" id="vchan_sum" name="vchan_sum" min="1" max="64" required>
                        <div class="help-text">支持的语音通道数量，最大64</div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3>端口配置</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="center_voice_port">语音起始端口</label>
                        <input type="number" id="center_voice_port" name="center_voice_port" min="1" max="65535" required>
                        <div class="help-text">中心席位语音端口起始值</div>
                    </div>
                    <div class="form-group">
                        <label for="listen_agent_port">监听代理端口</label>
                        <input type="number" id="listen_agent_port" name="listen_agent_port" min="1" max="65535" required>
                        <div class="help-text">监听代理服务端口</div>
                    </div>
                    <div class="form-group">
                        <label for="send_to_agent_port">发送代理端口</label>
                        <input type="number" id="send_to_agent_port" name="send_to_agent_port" min="1" max="65535" required>
                        <div class="help-text">发送到代理的端口</div>
                    </div>
                    <div class="form-group">
                        <label for="inssi_num">内应号个数</label>
                        <input type="number" id="inssi_num" name="inssi_num" min="1" max="65535" required>
                        <div class="help-text">内应号码个数</div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <h3>网络配置</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="peer_net_type">对等网络类型</label>
                        <select id="peer_net_type" name="peer_net_type" required>
                            <option value="0">单播 (Unicast)</option>
                            <option value="1">广播 (Broadcast)</option>
                            <option value="2">组播 (Multicast)</option>
                        </select>
                        <div class="help-text">网络通信方式</div>
                    </div>
                    <div class="form-group">
                        <label for="send_all_agent_ip">代理IP地址</label>
                        <input type="text" id="send_all_agent_ip" name="send_all_agent_ip" required pattern="^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$">
                        <div class="help-text">发送到所有代理的IP地址</div>
                    </div>
                    <div class="form-group">
                        <label for="spec_function">特殊功能</label>
                        <input type="number" id="spec_function" name="spec_function" min="0" max="65535">
                        <div class="help-text">特殊功能配置标志</div>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存配置</button>
                <button type="button" class="btn btn-secondary" onclick="loadConfig()">重新加载</button>
            </div>
        </form>
    </div>

    <script>
        // API基础URL
        const API_BASE_URL = '/api/v1';
        
        // 页面加载时自动加载配置
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
        });

        // 加载配置数据
        async function loadConfig() {
            showLoading(true);
            try {
                const response = await fetch(`${API_BASE_URL}/config/device/center`);
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    populateForm(result.data);
                    showMessage('配置加载成功', 'success');
                } else {
                    showMessage(result.message || '加载配置失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误：' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // 填充表单数据
        function populateForm(data) {
            Object.keys(data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = data[key];
                }
            });
        }

        // 表单提交处理
        document.getElementById('center-config-form').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            // 验证表单
            if (!validateForm()) {
                return;
            }
            
            // 收集表单数据
            const formData = collectFormData();
            
            showLoading(true);
            try {
                const response = await fetch(`${API_BASE_URL}/config/device/center`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    showMessage('配置保存成功', 'success');
                } else {
                    showMessage(result.message || '保存配置失败', 'error');
                }
            } catch (error) {
                showMessage('网络错误：' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        });

        // 收集表单数据
        function collectFormData() {
            const form = document.getElementById('center-config-form');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                // 数字类型字段转换
                if (['center_no', 'center_outssi', 'center_inssi', 'vchan_sum', 
                     'center_voice_port', 'listen_agent_port', 'send_to_agent_port', 
                     'inssi_num', 'peer_net_type', 'spec_function'].includes(key)) {
                    data[key] = parseInt(value) || 0;
                } else {
                    data[key] = value;
                }
            }
            
            return data;
        }

        // 表单验证
        function validateForm() {
            const form = document.getElementById('center-config-form');
            
            // HTML5表单验证
            if (!form.checkValidity()) {
                form.reportValidity();
                return false;
            }
            
            // 自定义验证
            const centerNo = parseInt(document.getElementById('center_no').value);
            if (centerNo < 1 || centerNo > 16777215) {
                showMessage('中心号码必须在1-16777215范围内', 'error');
                return false;
            }
            
            const outssi = parseInt(document.getElementById('center_outssi').value);
            if (outssi < 1 || outssi > 16777215) {
                showMessage('外部SSI必须在1-16777215范围内', 'error');
                return false;
            }
            
            const inssi = parseInt(document.getElementById('center_inssi').value);
            if (inssi < 1 || inssi > 16777215) {
                showMessage('内部SSI必须在1-16777215范围内', 'error');
                return false;
            }
            
            return true;
        }

        // 显示状态消息
        function showMessage(message, type) {
            const messageEl = document.getElementById('status-message');
            messageEl.textContent = message;
            messageEl.className = `status-message status-${type}`;
            messageEl.style.display = 'block';
            
            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    messageEl.style.display = 'none';
                }, 3000);
            }
        }

        // 显示/隐藏加载状态
        function showLoading(show) {
            const loadingEl = document.getElementById('loading');
            const formEl = document.getElementById('center-config-form');
            
            if (show) {
                loadingEl.style.display = 'block';
                formEl.style.display = 'none';
            } else {
                loadingEl.style.display = 'none';
                formEl.style.display = 'block';
            }
        }
    </script>
</body>
</html> 