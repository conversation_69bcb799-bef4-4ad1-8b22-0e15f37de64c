<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络配置 - VICTEL IP交换机配置系统</title>
    <style>
        /* 现代化样式设计 */
        :root {
            --primary-color: #00A6A6;
            --secondary-color: #993333;
            --background-color: #f5f5f5;
            --text-color: #333;
            --border-color: #ddd;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: 14px;
            color: var(--text-color);
            background-color: var(--background-color);
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 1rem;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--text-color);
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(0, 166, 166, 0.2);
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background-color: #008a8a;
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-secondary:hover {
            background-color: #772222;
        }

        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            display: none;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .current-config {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .loading {
            text-align: center;
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>网络配置</h1>
            <p>第二阶段重构演示 - REST API</p>
        </div>

        <div class="card">
            <h2>当前网络配置</h2>
            <div id="current-config" class="current-config">
                <div class="loading">正在加载当前配置...</div>
            </div>
            <button class="btn btn-secondary" onclick="loadCurrentConfig()">刷新配置</button>
        </div>

        <div class="card">
            <h2>网络配置设置</h2>
            
            <div id="status" class="status"></div>
            
            <form id="network-form">
                <div class="form-group">
                    <label for="ip">IP地址:</label>
                    <input type="text" id="ip" name="ip" placeholder="*************" required>
                </div>
                
                <div class="form-group">
                    <label for="mask">子网掩码:</label>
                    <input type="text" id="mask" name="mask" placeholder="*************" required>
                </div>
                
                <div class="form-group">
                    <label for="gateway">网关地址:</label>
                    <input type="text" id="gateway" name="gateway" placeholder="***********" required>
                </div>
                
                <div class="form-group">
                    <label for="dns">DNS服务器:</label>
                    <input type="text" id="dns" name="dns" placeholder="*******" required>
                </div>
                
                <div class="form-group">
                    <label for="mac">MAC地址:</label>
                    <input type="text" id="mac" name="mac" placeholder="00:11:22:33:44:55" required>
                </div>
                
                <button type="button" class="btn btn-primary" onclick="saveNetworkConfig()">保存配置</button>
                <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
            </form>
        </div>

        <div class="card">
            <h2>API 测试</h2>
            <p>第二阶段重构实现的功能:</p>
            <ul>
                <li>✅ 网络配置数据结构映射 (JSON ↔ 二进制)</li>
                <li>✅ 输入验证和错误处理</li>
                <li>✅ REST API接口 (GET/POST /api/v1/config/network)</li>
                <li>✅ 配置文件兼容性保持</li>
                <li>✅ 现代化前端界面</li>
            </ul>
        </div>
    </div>

    <script>
        const API_BASE = '/api/v1';
        
        // 显示状态消息
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${isError ? 'error' : 'success'}`;
            statusDiv.style.display = 'block';
            
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
        
        // 加载当前网络配置
        async function loadCurrentConfig() {
            try {
                const response = await fetch(`${API_BASE}/config/network`);
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    const config = result.data;
                    displayCurrentConfig(config);
                    fillForm(config);
                } else {
                    throw new Error(result.message || '获取配置失败');
                }
            } catch (error) {
                document.getElementById('current-config').innerHTML = 
                    `<div style="color: var(--danger-color);">加载失败: ${error.message}</div>`;
            }
        }
        
        // 显示当前配置
        function displayCurrentConfig(config) {
            const html = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
                    <div><strong>IP地址:</strong> ${config.ip}</div>
                    <div><strong>子网掩码:</strong> ${config.mask}</div>
                    <div><strong>网关:</strong> ${config.gateway}</div>
                    <div><strong>DNS:</strong> ${config.dns}</div>
                    <div><strong>MAC地址:</strong> ${config.mac}</div>
                </div>
            `;
            document.getElementById('current-config').innerHTML = html;
        }
        
        // 填充表单
        function fillForm(config) {
            document.getElementById('ip').value = config.ip || '';
            document.getElementById('mask').value = config.mask || '';
            document.getElementById('gateway').value = config.gateway || '';
            document.getElementById('dns').value = config.dns || '';
            document.getElementById('mac').value = config.mac || '';
        }
        
        // 保存网络配置
        async function saveNetworkConfig() {
            const formData = {
                ip: document.getElementById('ip').value,
                mask: document.getElementById('mask').value,
                gateway: document.getElementById('gateway').value,
                dns: document.getElementById('dns').value,
                mac: document.getElementById('mac').value
            };
            
            // 简单验证
            if (!formData.ip || !formData.mask || !formData.gateway || !formData.dns || !formData.mac) {
                showStatus('请填写所有字段', true);
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/config/network`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.code === 200) {
                    showStatus('配置保存成功');
                    // 重新加载当前配置
                    setTimeout(loadCurrentConfig, 1000);
                } else {
                    throw new Error(result.message || '保存失败');
                }
            } catch (error) {
                showStatus(`保存失败: ${error.message}`, true);
            }
        }
        
        // 重置表单
        function resetForm() {
            document.getElementById('network-form').reset();
        }
        
        // 页面加载完成后自动加载当前配置
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentConfig();
        });
    </script>
</body>
</html> 