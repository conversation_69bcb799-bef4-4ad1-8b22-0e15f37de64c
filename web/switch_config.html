<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交换机配置 - VICTEL IP交换机</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "微软雅黑", Arial, sans-serif;
            font-size: 13px;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: linear-gradient(135deg, #00A6A6, #008888);
            color: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: bold;
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .config-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        .panel-header {
            background: #993333;
            color: white;
            padding: 1rem 1.5rem;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .board-type-indicator {
            background: rgba(255,255,255,0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
        }

        .panel-content {
            padding: 2rem;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: bold;
            margin-bottom: 0.5rem;
            color: #555;
        }

        .form-input {
            padding: 0.8rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #00A6A6;
            box-shadow: 0 0 0 2px rgba(0, 166, 166, 0.1);
        }

        .form-input.error {
            border-color: #e74c3c;
        }

        .form-select {
            padding: 0.8rem;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 0.9rem;
            background: white;
            cursor: pointer;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .error-message {
            color: #e74c3c;
            font-size: 0.8rem;
            margin-top: 0.3rem;
        }

        .btn-group {
            display: flex;
            gap: 1rem;
            justify-content: flex-end;
            margin-top: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }

        .btn {
            padding: 0.8rem 2rem;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #00A6A6;
            color: white;
        }

        .btn-primary:hover {
            background: #008888;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #00A6A6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            padding: 1rem;
            border-radius: 4px;
            margin-bottom: 1rem;
            display: none;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .ip-switch-only, .module-4g-only, .voice-board-only {
            display: none;
        }

        .config-info {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 4px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .config-info h4 {
            color: #004085;
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-group {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>交换功能配置</h1>
    </div>

    <div class="container">
        <div id="message" class="message"></div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在加载配置...</p>
        </div>

        <form id="switchConfigForm">
            <!-- 设备信息 -->
            <div class="config-panel">
                <div class="panel-header">
                    设备基本信息
                    <div class="board-type-indicator" id="boardTypeIndicator">检测中...</div>
                </div>
                <div class="panel-content">
                    <div class="config-info" id="boardInfo">
                        <h4>板卡类型说明</h4>
                        <p>系统将自动检测当前板卡类型，请根据检测结果配置相应的参数。</p>
                    </div>
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="switchId">交换机ID *</label>
                            <input type="number" id="switchId" name="switch_id" class="form-input" 
                                   min="1" max="16777215" required>
                            <div class="error-message" id="switchIdError"></div>
                        </div>
                        <div class="form-group">
                            <label for="switchName">交换机名称 *</label>
                            <input type="text" id="switchName" name="switch_name" class="form-input" 
                                   maxlength="31" required>
                            <div class="error-message" id="switchNameError"></div>
                        </div>
                        <div class="form-group">
                            <label for="boardType">板卡类型</label>
                            <select id="boardType" name="board_type" class="form-select">
                                <option value="0">IP交换机</option>
                                <option value="1">4G模块</option>
                                <option value="2">语音板卡</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="boardVersion">板卡版本</label>
                            <input type="text" id="boardVersion" name="board_version" class="form-input" 
                                   maxlength="15" readonly>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 基础配置 -->
            <div class="config-panel">
                <div class="panel-header">基础配置</div>
                <div class="panel-content">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="switchMode">工作模式</label>
                            <select id="switchMode" name="switch_mode" class="form-select">
                                <option value="0">独立模式</option>
                                <option value="1">级联模式</option>
                                <option value="2">堆叠模式</option>
                            </select>
                        </div>
                        <div class="form-group ip-switch-only">
                            <label for="portCount">端口数量</label>
                            <select id="portCount" name="port_count" class="form-select">
                                <option value="8">8端口</option>
                                <option value="16">16端口</option>
                                <option value="24">24端口</option>
                                <option value="48">48端口</option>
                            </select>
                        </div>
                        <div class="form-group voice-board-only">
                            <label for="voiceChannels">语音通道数</label>
                            <input type="number" id="voiceChannels" name="voice_channels" class="form-input" 
                                   min="1" max="32" value="8">
                        </div>
                        <div class="form-group module-4g-only">
                            <label for="dataChannels">数据通道数</label>
                            <input type="number" id="dataChannels" name="data_channels" class="form-input" 
                                   min="1" max="16" value="4">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高级配置 -->
            <div class="config-panel">
                <div class="panel-header">高级配置</div>
                <div class="panel-content">
                    <div class="form-grid">
                        <div class="form-group ip-switch-only">
                            <label>启用Trunk</label>
                            <div class="checkbox-group">
                                <input type="checkbox" id="enableTrunk" name="enable_trunk">
                                <label for="enableTrunk">启用端口聚合</label>
                            </div>
                        </div>
                        <div class="form-group ip-switch-only">
                            <label for="trunkProtocol">Trunk协议</label>
                            <select id="trunkProtocol" name="trunk_protocol" class="form-select">
                                <option value="0">静态聚合</option>
                                <option value="1">LACP</option>
                                <option value="2">PAgP</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="qosPriority">QoS优先级</label>
                            <select id="qosPriority" name="qos_priority" class="form-select">
                                <option value="0">低</option>
                                <option value="1">普通</option>
                                <option value="2">高</option>
                                <option value="3">最高</option>
                            </select>
                        </div>
                        <div class="form-group ip-switch-only">
                            <label>自动协商</label>
                            <div class="checkbox-group">
                                <input type="checkbox" id="autoNegotiate" name="auto_negotiate" checked>
                                <label for="autoNegotiate">启用端口自动协商</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="btn-group">
                <button type="button" class="btn btn-secondary" onclick="resetForm()">重置</button>
                <button type="submit" class="btn btn-primary">保存配置</button>
            </div>
        </form>
    </div>

    <script>
        let currentBoardType = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            detectBoardType();
            loadSwitchConfig();
            
            // 板卡类型改变时切换界面
            document.getElementById('boardType').addEventListener('change', function() {
                switchBoardTypeUI(this.value);
            });

            // Trunk启用状态改变时的处理
            document.getElementById('enableTrunk').addEventListener('change', function() {
                const trunkProtocol = document.getElementById('trunkProtocol');
                trunkProtocol.disabled = !this.checked;
            });
        });

        // 检测板卡类型
        async function detectBoardType() {
            try {
                const response = await fetch('/api/v1/config/device/switch');
                const result = await response.json();
                
                if (result.code === 200 && result.data.detected_board_type !== undefined) {
                    currentBoardType = result.data.detected_board_type;
                    document.getElementById('boardType').value = currentBoardType;
                    updateBoardTypeIndicator(currentBoardType, result.data.board_type_name);
                    switchBoardTypeUI(currentBoardType);
                }
            } catch (error) {
                console.error('板卡类型检测失败:', error);
                showMessage('板卡类型检测失败，使用默认配置', 'error');
            }
        }

        // 更新板卡类型指示器
        function updateBoardTypeIndicator(boardType, typeName) {
            const indicator = document.getElementById('boardTypeIndicator');
            indicator.textContent = typeName || getBoardTypeName(boardType);
            
            // 更新说明信息
            const boardInfo = document.getElementById('boardInfo');
            let infoText = '';
            switch(parseInt(boardType)) {
                case 0:
                    infoText = '当前板卡类型：IP交换机。支持多端口以太网交换，可配置端口聚合和QoS等高级功能。';
                    break;
                case 1:
                    infoText = '当前板卡类型：4G模块。支持移动通信数据传输，可配置数据通道数和QoS优先级。';
                    break;
                case 2:
                    infoText = '当前板卡类型：语音板卡。专门用于语音通信处理，可配置语音通道数和编码方式。';
                    break;
                default:
                    infoText = '板卡类型检测中，请稍候...';
            }
            boardInfo.querySelector('p').textContent = infoText;
        }

        // 获取板卡类型名称
        function getBoardTypeName(boardType) {
            const names = ['IP交换机', '4G模块', '语音板卡'];
            return names[parseInt(boardType)] || '未知类型';
        }

        // 切换板卡类型UI
        function switchBoardTypeUI(boardType) {
            const ipSwitchElements = document.querySelectorAll('.ip-switch-only');
            const module4gElements = document.querySelectorAll('.module-4g-only');
            const voiceBoardElements = document.querySelectorAll('.voice-board-only');
            
            // 隐藏所有特定类型元素
            ipSwitchElements.forEach(el => el.style.display = 'none');
            module4gElements.forEach(el => el.style.display = 'none');
            voiceBoardElements.forEach(el => el.style.display = 'none');
            
            // 显示对应类型的元素
            switch(parseInt(boardType)) {
                case 0: // IP交换机
                    ipSwitchElements.forEach(el => el.style.display = 'flex');
                    break;
                case 1: // 4G模块
                    module4gElements.forEach(el => el.style.display = 'flex');
                    break;
                case 2: // 语音板卡
                    voiceBoardElements.forEach(el => el.style.display = 'flex');
                    break;
            }
        }

        // 加载交换机配置
        async function loadSwitchConfig() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/v1/config/device/switch');
                const result = await response.json();
                
                if (result.code === 200) {
                    populateForm(result.data);
                    showMessage('配置加载成功', 'success');
                } else {
                    showMessage(result.message || '配置加载失败', 'error');
                }
            } catch (error) {
                console.error('加载配置失败:', error);
                showMessage('配置加载失败，请检查网络连接', 'error');
            } finally {
                showLoading(false);
            }
        }

        // 填充表单数据
        function populateForm(data) {
            Object.keys(data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = !!data[key];
                    } else {
                        element.value = data[key];
                    }
                }
            });
            
            // 特殊处理 Trunk 协议状态
            const enableTrunk = document.getElementById('enableTrunk');
            const trunkProtocol = document.getElementById('trunkProtocol');
            trunkProtocol.disabled = !enableTrunk.checked;
        }

        // 表单提交处理
        document.getElementById('switchConfigForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!validateForm()) {
                return;
            }
            
            const formData = collectFormData();
            
            try {
                showLoading(true);
                
                const response = await fetch('/api/v1/config/device/switch', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('交换机配置保存成功', 'success');
                } else {
                    showMessage(result.message || '配置保存失败', 'error');
                }
            } catch (error) {
                console.error('保存配置失败:', error);
                showMessage('配置保存失败，请检查网络连接', 'error');
            } finally {
                showLoading(false);
            }
        });

        // 收集表单数据
        function collectFormData() {
            const formData = {};
            const form = document.getElementById('switchConfigForm');
            const inputs = form.querySelectorAll('input, select');
            
            inputs.forEach(input => {
                const name = input.name;
                let value = input.value;
                
                if (input.type === 'checkbox') {
                    value = input.checked ? 1 : 0;
                } else if (input.type === 'number') {
                    value = parseInt(value) || 0;
                }
                
                if (name) {
                    formData[name] = value;
                }
            });
            
            return formData;
        }

        // 表单验证
        function validateForm() {
            let isValid = true;
            
            // 验证交换机ID
            const switchId = document.getElementById('switchId');
            if (!switchId.value || switchId.value < 1 || switchId.value > 16777215) {
                showFieldError('switchIdError', '交换机ID必须在1-16777215范围内');
                isValid = false;
            } else {
                clearFieldError('switchIdError');
            }
            
            // 验证交换机名称
            const switchName = document.getElementById('switchName');
            if (!switchName.value.trim()) {
                showFieldError('switchNameError', '交换机名称不能为空');
                isValid = false;
            } else {
                clearFieldError('switchNameError');
            }
            
            return isValid;
        }

        // 显示字段错误
        function showFieldError(errorId, message) {
            const errorElement = document.getElementById(errorId);
            const inputElement = errorElement.previousElementSibling;
            
            errorElement.textContent = message;
            inputElement.classList.add('error');
        }

        // 清除字段错误
        function clearFieldError(errorId) {
            const errorElement = document.getElementById(errorId);
            const inputElement = errorElement.previousElementSibling;
            
            errorElement.textContent = '';
            inputElement.classList.remove('error');
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要重置所有配置吗？')) {
                document.getElementById('switchConfigForm').reset();
                loadSwitchConfig();
            }
        }

        // 显示加载状态
        function showLoading(show) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 显示消息
        function showMessage(text, type) {
            const messageElement = document.getElementById('message');
            messageElement.textContent = text;
            messageElement.className = `message ${type}`;
            messageElement.style.display = 'block';
            
            setTimeout(() => {
                messageElement.style.display = 'none';
            }, 5000);
        }
    </script>
</body>
</html> 