# api模块CMakeLists.txt

set(API_SOURCES
    auth_handler.c
    config_handler.c
    system_handler.c
)

set(API_HEADERS
    ${CMAKE_SOURCE_DIR}/include/api/auth_handler.h
    ${CMAKE_SOURCE_DIR}/include/api/config_handler.h
    ${CMAKE_SOURCE_DIR}/include/api/system_handler.h
)

# 创建静态库
add_library(webcfg-api STATIC ${API_SOURCES})

# 包含目录
target_include_directories(webcfg-api
    PUBLIC ${CMAKE_SOURCE_DIR}/include
    PRIVATE ${CMAKE_SOURCE_DIR}/include/api
)

# 链接依赖
target_link_libraries(webcfg-api
    webcfg-core
    webcfg-config
    webcfg-utils
    cjson
) 