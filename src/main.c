#include <stdio.h>
#include <stdlib.h>
#include <signal.h>
#include <unistd.h>

#include "core/http_server.h"
#include "core/router.h"
#include "api/auth_handler.h"
#include "api/config_handler.h"
#include "api/system_handler.h"
#include "config/config_manager.h"

// 全局变量
static http_server_t *g_server = NULL;
static router_t *g_router = NULL;
static int g_running = 1;

// 信号处理函数
void signal_handler(int sig) {
    printf("接收到信号 %d，准备退出...\n", sig);
    g_running = 0;
    
    if (g_server) {
        http_server_stop(g_server);
    }
}

// 注册API路由
int register_api_routes(router_t *router) {
    // 认证相关API
    router_register(router, "POST", "/api/v1/auth/login", auth_handle_login, 0);
    router_register(router, "POST", "/api/v1/auth/logout", auth_handle_logout, 1);
    router_register(router, "GET", "/api/v1/auth/status", auth_handle_status, 0);
    
    // 配置相关API (将在config_handler中实现)
    router_register(router, "GET", "/api/v1/config/network", config_handle_network_get, 1);
    router_register(router, "POST", "/api/v1/config/network", config_handle_network_post, 1);
    router_register(router, "GET", "/api/v1/config/device/*", config_handle_device_get, 1);
    router_register(router, "POST", "/api/v1/config/device/*", config_handle_device_post, 1);
    
    // 系统相关API (将在system_handler中实现)
    router_register(router, "GET", "/api/v1/system/info", system_handle_info, 1);
    router_register(router, "POST", "/api/v1/system/reboot", system_handle_reboot, 1);
    router_register(router, "GET", "/api/v1/system/logs", system_handle_logs, 1);
    
    return 0;
}

int main(int argc, char *argv[]) {
    int port = 80;  // 默认端口
    char *web_root = "./web";  // 默认网页根目录
    
    // 解析命令行参数
    int opt;
    while ((opt = getopt(argc, argv, "p:w:h")) != -1) {
        switch (opt) {
            case 'p':
                port = atoi(optarg);
                if (port <= 0 || port > 65535) {
                    fprintf(stderr, "无效端口号: %d\n", port);
                    exit(EXIT_FAILURE);
                }
                break;
            case 'w':
                web_root = optarg;
                break;
            case 'h':
                printf("用法: %s [-p port] [-w web_root] [-h]\n", argv[0]);
                printf("  -p port      监听端口 (默认: 80)\n");
                printf("  -w web_root  网页根目录 (默认: ./web)\n");
                printf("  -h           显示帮助信息\n");
                exit(EXIT_SUCCESS);
            default:
                fprintf(stderr, "用法: %s [-p port] [-w web_root] [-h]\n", argv[0]);
                exit(EXIT_FAILURE);
        }
    }
    
    printf("VICTEL IP交换机配置系统启动中...\n");
    printf("监听端口: %d\n", port);
    printf("网页根目录: %s\n", web_root);
    
    // 注册信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 初始化各个子系统
    if (auth_init() != 0) {
        fprintf(stderr, "初始化认证系统失败\n");
        exit(EXIT_FAILURE);
    }
    
    if (config_manager_init() != 0) {
        fprintf(stderr, "初始化配置管理器失败\n");
        exit(EXIT_FAILURE);
    }
    
    // 创建路由器
    g_router = router_init();
    if (!g_router) {
        fprintf(stderr, "创建路由器失败\n");
        exit(EXIT_FAILURE);
    }
    
    // 注册API路由
    if (register_api_routes(g_router) != 0) {
        fprintf(stderr, "注册API路由失败\n");
        exit(EXIT_FAILURE);
    }
    
    // 配置HTTP服务器
    http_server_config_t server_config = {
        .port = port,
        .web_root = web_root,
        .max_connections = 100,
        .timeout = 30
    };
    
    // 创建HTTP服务器
    g_server = http_server_init(&server_config);
    if (!g_server) {
        fprintf(stderr, "创建HTTP服务器失败\n");
        exit(EXIT_FAILURE);
    }
    
    // 启动HTTP服务器
    if (http_server_start(g_server) != 0) {
        fprintf(stderr, "启动HTTP服务器失败\n");
        exit(EXIT_FAILURE);
    }
    
    printf("服务器启动成功，访问地址:\n");
    printf("  本地: http://localhost:%d\n", port);
    printf("  网络: http://[your-ip]:%d\n", port);
    printf("按 Ctrl+C 退出服务器\n");
    
    // 主循环
    while (g_running) {
        sleep(1);
    }
    
    printf("正在关闭服务器...\n");
    
    // 清理资源
    if (g_server) {
        http_server_destroy(g_server);
    }
    
    if (g_router) {
        router_destroy(g_router);
    }
    
    config_manager_destroy();
    auth_destroy();
    
    printf("服务器已关闭\n");
    return EXIT_SUCCESS;
} 