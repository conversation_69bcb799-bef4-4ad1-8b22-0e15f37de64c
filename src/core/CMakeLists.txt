# core模块CMakeLists.txt

set(CORE_SOURCES
    http_server.c
    router.c
    response.c
)

set(CORE_HEADERS
    ${CMAKE_SOURCE_DIR}/include/core/http_server.h
    ${CMAKE_SOURCE_DIR}/include/core/router.h
    ${CMAKE_SOURCE_DIR}/include/core/response.h
)

# 创建静态库
add_library(webcfg-core STATIC ${CORE_SOURCES})

# 包含目录
target_include_directories(webcfg-core
    PUBLIC ${CMAKE_SOURCE_DIR}/include
    PRIVATE ${CMAKE_SOURCE_DIR}/include/core
)

# 链接依赖
target_link_libraries(webcfg-core
    cjson
) 