#include "center_config.h"
#include "core/response.h"
#include <stdio.h>
#include <string.h>
#include <microhttpd.h>

/**
 * @file center_api.c
 * @brief 呼叫中心API处理器
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 从config_handler.c迁移呼叫中心相关API处理逻辑
 */

/**
 * 处理呼叫中心配置获取请求
 * GET /api/v1/config/device/center
 */
int center_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    center_config_binary_t binary_config;
    center_config_json_t json_config;
    
    // 读取呼叫中心配置文件
    if (read_center_config_file(&binary_config) != 0) {
        return response_send_error(connection, 500, "Failed to read center configuration");
    }
    
    // 转换为JSON格式
    if (convert_center_binary_to_json(&binary_config, &json_config) != 0) {
        return response_send_error(connection, 500, "Failed to convert center configuration");
    }
    
    // 创建JSON响应对象
    cJSON *json_data = cJSON_CreateObject();
    cJSON_AddNumberToObject(json_data, "center_no", json_config.center_no);
    cJSON_AddNumberToObject(json_data, "center_outssi", json_config.center_outssi);
    cJSON_AddNumberToObject(json_data, "center_inssi", json_config.center_inssi);
    cJSON_AddNumberToObject(json_data, "vchan_sum", json_config.vchan_sum);
    cJSON_AddNumberToObject(json_data, "center_voice_port", json_config.center_voice_port);
    cJSON_AddNumberToObject(json_data, "listen_agent_port", json_config.listen_agent_port);
    cJSON_AddNumberToObject(json_data, "peer_net_type", json_config.peer_net_type);
    cJSON_AddStringToObject(json_data, "send_all_agent_ip", json_config.send_all_agent_ip);
    cJSON_AddNumberToObject(json_data, "send_to_agent_port", json_config.send_to_agent_port);
    cJSON_AddNumberToObject(json_data, "inssi_num", json_config.inssi_num);
    cJSON_AddNumberToObject(json_data, "spec_function", json_config.spec_function);
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 处理呼叫中心配置保存请求
 * POST /api/v1/config/device/center
 */
int center_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }
    
    // 解析JSON数据
    cJSON *json = cJSON_Parse(request_data);
    if (!json) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    center_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));
    
    // 从JSON提取配置数据
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "center_no");
    if (cJSON_IsNumber(item)) {
        json_config.center_no = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_outssi");
    if (cJSON_IsNumber(item)) {
        json_config.center_outssi = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_inssi");
    if (cJSON_IsNumber(item)) {
        json_config.center_inssi = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "vchan_sum");
    if (cJSON_IsNumber(item)) {
        json_config.vchan_sum = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "center_voice_port");
    if (cJSON_IsNumber(item)) {
        json_config.center_voice_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "listen_agent_port");
    if (cJSON_IsNumber(item)) {
        json_config.listen_agent_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "peer_net_type");
    if (cJSON_IsNumber(item)) {
        json_config.peer_net_type = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "send_all_agent_ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.send_all_agent_ip, item->valuestring, sizeof(json_config.send_all_agent_ip) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "send_to_agent_port");
    if (cJSON_IsNumber(item)) {
        json_config.send_to_agent_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "inssi_num");
    if (cJSON_IsNumber(item)) {
        json_config.inssi_num = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "spec_function");
    if (cJSON_IsNumber(item)) {
        json_config.spec_function = (uint16_t)item->valueint;
    }
    
    // 验证配置数据
    if (validate_center_config(&json_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 400, "Invalid center configuration data");
    }
    
    // JSON转换为二进制配置
    center_config_binary_t binary_config;
    if (convert_center_json_to_binary(&json_config, &binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to convert configuration data");
    }
    
    // 写入配置文件
    if (write_center_config_file(&binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to save center configuration");
    }
    
    cJSON_Delete(json);
    
    // 发送成功响应 
    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "Center configuration saved successfully");
    int result = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return result;
}

/**
 * 呼叫中心配置路由处理器
 */
int center_handle_config_request(struct MHD_Connection *connection, const char *method, 
                                const char *url, const char *request_data) {
    if (strcmp(method, "GET") == 0) {
        return center_handle_config_get(connection, url, request_data);
    } else if (strcmp(method, "POST") == 0) {
        return center_handle_config_post(connection, url, request_data);
    } else {
        return response_send_error(connection, 405, "Method not allowed");
    }
} 