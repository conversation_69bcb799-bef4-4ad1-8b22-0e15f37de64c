#ifndef CONFIG_CENTER_CENTER_CONFIG_H
#define CONFIG_CENTER_CENTER_CONFIG_H

#include "config/common/config_base.h"
#include <stdint.h>
#include <cJSON.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file center_config.h
 * @brief 呼叫中心配置模块
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 基于deprecated/cgi/0center.c和1rwcenter.h，确保100%兼容
 */

// 配置文件路径定义（与原始保持一致）
#define CALLCENTERCFG   "/home/<USER>/cfg/callcenter.cfg"
#define START_ADDR_CALLCENTER  0x00

// 呼叫中心配置二进制结构体（与deprecated/cgi/inc/1rwcenter.h 100%兼容）
typedef struct {
    uint32_t center_no:24;          // 中心号码（24位）
    uint32_t center_outssi:24;      // 中心台统一外部号（24位）
    uint32_t center_inssi:24;       // 中心台统一内应号（24位）
    uint8_t vchan_sum;              // 语音通道数
    uint16_t center_voice_port;     // 中心席位语音起始端口
    uint16_t listen_agent_port;     // 监听代理端口
    uint8_t peer_net_type;          // 对等网络类型
    uint32_t send_all_agent_ip;     // 发送所有代理IP
    uint16_t send_to_agent_port;    // peer接收端口
    uint16_t inssi_num;             // 内应号个数
    uint16_t spec_function;         // 特殊功能
} __attribute__((packed)) center_config_binary_t;

// JSON格式配置（用于API接口）
typedef struct {
    uint32_t center_no;             // 中心号码
    uint32_t center_outssi;         // 中心台统一外部号
    uint32_t center_inssi;          // 中心台统一内应号
    uint8_t vchan_sum;              // 语音通道数
    uint16_t center_voice_port;     // 中心席位语音起始端口
    uint16_t listen_agent_port;     // 监听代理端口
    uint8_t peer_net_type;          // 对等网络类型
    char send_all_agent_ip[16];     // 发送所有代理IP（字符串格式）
    uint16_t send_to_agent_port;    // peer接收端口
    uint16_t inssi_num;             // 内应号个数
    uint16_t spec_function;         // 特殊功能
} center_config_json_t;

// 配置操作接口
extern const config_operations_t center_config_ops;

// 核心配置操作函数
int center_config_read(center_config_binary_t *config);
int center_config_write(const center_config_binary_t *config);
int center_config_validate(const center_config_json_t *config);
int center_config_json_to_binary(const center_config_json_t *json, center_config_binary_t *binary);
int center_config_binary_to_json(const center_config_binary_t *binary, center_config_json_t *json);
void center_config_set_default(center_config_binary_t *config);

// 兼容性函数（保持与原有device_config.c的接口一致）
int validate_center_config(const center_config_json_t *config);
int convert_center_json_to_binary(const center_config_json_t *json_config, center_config_binary_t *binary_config);
int convert_center_binary_to_json(const center_config_binary_t *binary_config, center_config_json_t *json_config);
int read_center_config_file(center_config_binary_t *config);
int write_center_config_file(const center_config_binary_t *config);
void set_default_center_config(center_config_binary_t *config);

// 工具函数
void center_ip_num_to_str(uint32_t ip_num, char *ip_str, size_t size);
uint32_t center_ip_str_to_num(const char *ip_str);

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_CENTER_CENTER_CONFIG_H */ 