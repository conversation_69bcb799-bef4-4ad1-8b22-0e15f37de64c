#include "center_config.h"
#include "utils/validation/ip_validator.h"
#include "utils/conversion/ip_converter.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>

/**
 * @file center_config.c
 * @brief 呼叫中心配置模块实现
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 基于deprecated/cgi/0center.c的业务逻辑，确保100%兼容
 */

// ==================== 核心配置操作函数 ====================

/**
 * 读取呼叫中心配置
 */
int center_config_read(center_config_binary_t *config) {
    return read_center_config_file(config);
}

/**
 * 写入呼叫中心配置
 */
int center_config_write(const center_config_binary_t *config) {
    return write_center_config_file(config);
}

/**
 * 验证呼叫中心配置（基于JSON格式）
 */
int center_config_validate(const center_config_json_t *config) {
    return validate_center_config(config);
}

/**
 * JSON转二进制配置
 */
int center_config_json_to_binary(const center_config_json_t *json, center_config_binary_t *binary) {
    return convert_center_json_to_binary(json, binary);
}

/**
 * 二进制转JSON配置
 */
int center_config_binary_to_json(const center_config_binary_t *binary, center_config_json_t *json) {
    return convert_center_binary_to_json(binary, json);
}

/**
 * 设置默认配置
 */
void center_config_set_default(center_config_binary_t *config) {
    set_default_center_config(config);
}

// ==================== 兼容性函数实现 ====================

/**
 * 验证呼叫中心配置数据
 * 保持与原有device_config.c中实现完全一致
 */
int validate_center_config(const center_config_json_t *config) {
    if (!config) {
        return -1;
    }
    
    // 验证中心号码（24位有效）
    if (config->center_no > 0xFFFFFF) {
        return -1;
    }
    
    // 验证外部SSI（24位有效）
    if (config->center_outssi > 0xFFFFFF) {
        return -1;
    }
    
    // 验证内部SSI（24位有效）
    if (config->center_inssi > 0xFFFFFF) {
        return -1;
    }
    
    // 验证语音通道数（合理范围）
    if (config->vchan_sum > 64) {
        return -1;
    }
    
    // 验证端口范围（uint16_t类型无需检查上限）
    if (config->center_voice_port == 0) {
        return -1;
    }
    
    if (config->listen_agent_port == 0) {
        return -1;
    }
    
    if (config->send_to_agent_port == 0) {
        return -1;
    }
    
    // 验证对等网络类型
    if (config->peer_net_type > 2) {
        return -1;
    }
    
    // 验证IP地址格式
    if (!validate_ip_address(config->send_all_agent_ip)) {
        return -1;
    }
    
    return 0;
}

/**
 * JSON格式转换为二进制配置
 * 保持与原有device_config.c中实现完全一致
 */
int convert_center_json_to_binary(const center_config_json_t *json_config, 
                                center_config_binary_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }
    
    // 清零结构体
    memset(binary_config, 0, sizeof(center_config_binary_t));
    
    // 转换字段（注意24位字段的特殊处理）
    binary_config->center_no = json_config->center_no & 0xFFFFFF;
    binary_config->center_outssi = json_config->center_outssi & 0xFFFFFF;
    binary_config->center_inssi = json_config->center_inssi & 0xFFFFFF;
    binary_config->vchan_sum = json_config->vchan_sum;
    binary_config->center_voice_port = json_config->center_voice_port;
    binary_config->listen_agent_port = json_config->listen_agent_port;
    binary_config->peer_net_type = json_config->peer_net_type;
    binary_config->send_to_agent_port = json_config->send_to_agent_port;
    binary_config->inssi_num = json_config->inssi_num;
    binary_config->spec_function = json_config->spec_function;
    
    // 转换IP地址字符串为数字
    binary_config->send_all_agent_ip = center_ip_str_to_num(json_config->send_all_agent_ip);
    
    return 0;
}

/**
 * 二进制配置转换为JSON格式
 * 保持与原有device_config.c中实现完全一致
 */
int convert_center_binary_to_json(const center_config_binary_t *binary_config, 
                                 center_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }
    
    // 清零结构体
    memset(json_config, 0, sizeof(center_config_json_t));
    
    // 转换字段
    json_config->center_no = binary_config->center_no;
    json_config->center_outssi = binary_config->center_outssi;
    json_config->center_inssi = binary_config->center_inssi;
    json_config->vchan_sum = binary_config->vchan_sum;
    json_config->center_voice_port = binary_config->center_voice_port;
    json_config->listen_agent_port = binary_config->listen_agent_port;
    json_config->peer_net_type = binary_config->peer_net_type;
    json_config->send_to_agent_port = binary_config->send_to_agent_port;
    json_config->inssi_num = binary_config->inssi_num;
    json_config->spec_function = binary_config->spec_function;
    
    // 转换IP地址数字为字符串
    center_ip_num_to_str(binary_config->send_all_agent_ip, json_config->send_all_agent_ip, 
                  sizeof(json_config->send_all_agent_ip));
    
    return 0;
}

/**
 * 读取呼叫中心配置文件
 * 保持与原有device_config.c中实现完全一致
 */
int read_center_config_file(center_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    FILE *fp = fopen(CALLCENTERCFG, "rb");
    if (!fp) {
        // 文件不存在，设置默认值
        set_default_center_config(config);
        return 0;
    }
    
    // 跳到指定偏移位置
    if (fseek(fp, START_ADDR_CALLCENTER, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 读取配置数据
    size_t read_size = fread(config, sizeof(center_config_binary_t), 1, fp);
    fclose(fp);
    
    if (read_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 写入呼叫中心配置文件
 * 保持与原有device_config.c中实现完全一致
 */
int write_center_config_file(const center_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    // 先尝试读取现有文件
    FILE *fp = fopen(CALLCENTERCFG, "r+b");
    if (!fp) {
        // 文件不存在，创建新文件
        fp = fopen(CALLCENTERCFG, "wb");
        if (!fp) {
            return -1;
        }
    }
    
    // 跳到指定偏移位置
    if (fseek(fp, START_ADDR_CALLCENTER, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 写入配置数据
    size_t write_size = fwrite(config, sizeof(center_config_binary_t), 1, fp);
    fclose(fp);
    
    if (write_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 设置呼叫中心配置默认值
 * 基于原有device_config.c的实现，参考原有center.cgi的默认值设置逻辑
 */
void set_default_center_config(center_config_binary_t *config) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(center_config_binary_t));
    
    // 设置默认值（参考原有逻辑）
    config->center_no = 0x000001;           // 默认中心号码
    config->center_outssi = 0x000001;       // 默认外部SSI  
    config->center_inssi = 0x000001;        // 默认内部SSI
    config->vchan_sum = 8;                  // 默认8个语音通道
    config->center_voice_port = 3000;       // 默认语音端口起始
    config->listen_agent_port = 2800;       // 默认监听端口
    config->peer_net_type = 0;              // 默认单播
    config->send_all_agent_ip = inet_addr("*************");  // 默认广播地址
    config->send_to_agent_port = 2900;      // 默认发送端口
    config->inssi_num = 100;                // 默认内应号个数
    config->spec_function = 0;              // 默认特殊功能
}

// ==================== 工具函数 ====================

/**
 * IP地址数字转字符串
 */
void center_ip_num_to_str(uint32_t ip_num, char *ip_str, size_t size) {
    if (!ip_str || size < 16) {
        return;
    }
    
    struct in_addr addr;
    addr.s_addr = ip_num;
    strncpy(ip_str, inet_ntoa(addr), size - 1);
    ip_str[size - 1] = '\0';
}

/**
 * IP地址字符串转数字
 */
uint32_t center_ip_str_to_num(const char *ip_str) {
    if (!ip_str) {
        return 0;
    }
    
    return inet_addr(ip_str);
}

// ==================== 统一配置接口实现 ====================

/**
 * 统一配置操作接口实例
 */
const config_operations_t center_config_ops = {
    .config_name = "center",
    .config_description = "呼叫中心配置",
    .json_config_size = sizeof(center_config_json_t),
    .binary_config_size = sizeof(center_config_binary_t),
    
    .validate_json = (config_validator_func_t)center_config_validate,
    .json_to_binary = (config_converter_func_t)center_config_json_to_binary,
    .binary_to_json = (config_converter_func_t)center_config_binary_to_json,
    .read_file = (config_file_func_t)center_config_read,
    .write_file = (config_file_write_func_t)center_config_write,
    .set_default = (config_default_func_t)center_config_set_default,
    
    .custom_data = NULL
}; 