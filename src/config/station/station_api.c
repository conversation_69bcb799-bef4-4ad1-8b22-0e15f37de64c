#include "station_config.h"
#include "core/response.h"
#include <stdio.h>
#include <string.h>
#include <microhttpd.h>

/**
 * @file station_api.c
 * @brief 基站API处理器
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 从config_handler.c迁移基站相关API处理逻辑
 */

/**
 * 处理基站配置获取请求
 * GET /api/v1/config/device/station
 */
int station_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    station_config_binary_t binary_config;
    station_config_json_t json_config;
    
    // 读取基站配置文件
    if (read_station_config_file(&binary_config) != 0) {
        return response_send_error(connection, 500, "Failed to read station configuration");
    }
    
    // 转换为JSON格式
    if (convert_station_binary_to_json(&binary_config, &json_config) != 0) {
        return response_send_error(connection, 500, "Failed to convert station configuration");
    }
    
    // 创建JSON响应对象
    cJSON *json_data = cJSON_CreateObject();
    cJSON_AddNumberToObject(json_data, "station_id", json_config.station_id);
    cJSON_AddNumberToObject(json_data, "get_cfg_method", json_config.get_cfg_method);
    cJSON_AddNumberToObject(json_data, "network_mode", json_config.network_mode);
    cJSON_AddStringToObject(json_data, "voice_ip", json_config.voice_ip);
    cJSON_AddNumberToObject(json_data, "data_listen_port", json_config.data_listen_port);
    cJSON_AddNumberToObject(json_data, "vbus_base_port", json_config.vbus_base_port);
    cJSON_AddNumberToObject(json_data, "net_address", json_config.net_address);
    cJSON_AddNumberToObject(json_data, "base_type", json_config.base_type);
    cJSON_AddStringToObject(json_data, "base_type_name", get_station_type_string((station_type_t)json_config.base_type));
    cJSON_AddNumberToObject(json_data, "vcan_number", json_config.vcan_number);
    cJSON_AddNumberToObject(json_data, "buffertime", json_config.buffertime);
    cJSON_AddNumberToObject(json_data, "downtime", json_config.downtime);
    cJSON_AddNumberToObject(json_data, "resettime", json_config.resettime);
    
    // 添加语音总线映射数组
    cJSON *vbus_array = cJSON_CreateArray();
    for (int i = 0; i < 12; i++) {
        cJSON_AddItemToArray(vbus_array, cJSON_CreateNumber(json_config.vbus_to_chan[i]));
    }
    cJSON_AddItemToObject(json_data, "vbus_to_chan", vbus_array);
    
    // 添加扩展字段
    cJSON_AddStringToObject(json_data, "station_name", json_config.station_name);
    cJSON_AddNumberToObject(json_data, "signal_strength", json_config.signal_strength);
    cJSON_AddBoolToObject(json_data, "auto_register", json_config.auto_register);
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 处理基站配置保存请求
 * POST /api/v1/config/device/station
 */
int station_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data) {
    if (!request_data) {
        return response_send_error(connection, 400, "Missing request data");
    }
    
    // 解析JSON数据
    cJSON *json = cJSON_Parse(request_data);
    if (!json) {
        return response_send_error(connection, 400, "Invalid JSON format");
    }
    
    station_config_json_t json_config;
    memset(&json_config, 0, sizeof(json_config));
    
    // 从JSON提取配置数据
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "get_cfg_method");
    if (cJSON_IsNumber(item)) {
        json_config.get_cfg_method = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "network_mode");
    if (cJSON_IsNumber(item)) {
        json_config.network_mode = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "voice_ip");
    if (cJSON_IsString(item)) {
        strncpy(json_config.voice_ip, item->valuestring, sizeof(json_config.voice_ip) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "data_listen_port");
    if (cJSON_IsNumber(item)) {
        json_config.data_listen_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "vbus_base_port");
    if (cJSON_IsNumber(item)) {
        json_config.vbus_base_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "net_address");
    if (cJSON_IsNumber(item)) {
        json_config.net_address = (uint32_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "base_type");
    if (cJSON_IsNumber(item)) {
        json_config.base_type = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "vcan_number");
    if (cJSON_IsNumber(item)) {
        json_config.vcan_number = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "buffertime");
    if (cJSON_IsNumber(item)) {
        json_config.buffertime = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "downtime");
    if (cJSON_IsNumber(item)) {
        json_config.downtime = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "resettime");
    if (cJSON_IsNumber(item)) {
        json_config.resettime = (uint8_t)item->valueint;
    }
    
    // 提取语音总线映射数组
    item = cJSON_GetObjectItem(json, "vbus_to_chan");
    if (cJSON_IsArray(item)) {
        int array_size = cJSON_GetArraySize(item);
        for (int i = 0; i < 12 && i < array_size; i++) {
            cJSON *element = cJSON_GetArrayItem(item, i);
            if (cJSON_IsNumber(element)) {
                json_config.vbus_to_chan[i] = (uint8_t)element->valueint;
            }
        }
    }
    
    // 提取扩展字段
    item = cJSON_GetObjectItem(json, "station_name");
    if (cJSON_IsString(item)) {
        strncpy(json_config.station_name, item->valuestring, sizeof(json_config.station_name) - 1);
    }
    
    item = cJSON_GetObjectItem(json, "signal_strength");
    if (cJSON_IsNumber(item)) {
        json_config.signal_strength = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "auto_register");
    if (cJSON_IsBool(item)) {
        json_config.auto_register = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    // 验证配置数据
    if (validate_station_config(&json_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 400, "Invalid station configuration data");
    }
    
    // JSON转换为二进制配置
    station_config_binary_t binary_config;
    if (convert_station_json_to_binary(&json_config, &binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to convert configuration data");
    }
    
    // 写入配置文件
    if (write_station_config_file(&binary_config) != 0) {
        cJSON_Delete(json);
        return response_send_error(connection, 500, "Failed to save station configuration");
    }
    
    cJSON_Delete(json);
    
    // 发送成功响应 
    cJSON *success_data = cJSON_CreateObject();
    cJSON_AddStringToObject(success_data, "message", "Station configuration saved successfully");
    cJSON_AddStringToObject(success_data, "station_type", get_station_type_string((station_type_t)json_config.base_type));
    int result = response_send_success(connection, success_data);
    cJSON_Delete(success_data);
    return result;
}

/**
 * 处理设备类型检测请求
 * GET /api/v1/system/device/type
 */
int station_handle_device_type_get(struct MHD_Connection *connection, const char *url, const char *request_data) {
    // 检测设备类型
    station_type_t detected_type = detect_station_type();
    
    // 创建JSON响应
    cJSON *json_data = cJSON_CreateObject();
    cJSON_AddNumberToObject(json_data, "device_type", detected_type);
    cJSON_AddStringToObject(json_data, "device_type_name", get_station_type_string(detected_type));
    cJSON_AddStringToObject(json_data, "detection_method", "hardware_detection");
    
    // 发送成功响应
    int result = response_send_success(connection, json_data);
    
    if (json_data) {
        cJSON_Delete(json_data);
    }
    
    return result;
}

/**
 * 基站配置路由处理器
 */
int station_handle_config_request(struct MHD_Connection *connection, const char *method, 
                                 const char *url, const char *request_data) {
    if (strcmp(method, "GET") == 0) {
        // 检查是否是设备类型检测请求
        if (strstr(url, "/system/device/type")) {
            return station_handle_device_type_get(connection, url, request_data);
        } else {
            return station_handle_config_get(connection, url, request_data);
        }
    } else if (strcmp(method, "POST") == 0) {
        return station_handle_config_post(connection, url, request_data);
    } else {
        return response_send_error(connection, 405, "Method not allowed");
    }
} 