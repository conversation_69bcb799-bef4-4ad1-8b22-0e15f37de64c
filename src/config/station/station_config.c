#include "station_config.h"
#include "utils/validation/ip_validator.h"
#include "utils/conversion/ip_converter.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>

/**
 * @file station_config.c
 * @brief 基站配置模块实现
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 基于deprecated/cgi/0sci*.c的业务逻辑，确保100%兼容
 */

// ==================== 核心配置操作函数 ====================

/**
 * 读取基站配置
 */
int station_config_read(station_config_binary_t *config) {
    return read_station_config_file(config);
}

/**
 * 写入基站配置
 */
int station_config_write(const station_config_binary_t *config) {
    return write_station_config_file(config);
}

/**
 * 验证基站配置（基于JSON格式）
 */
int station_config_validate(const station_config_json_t *config) {
    return validate_station_config(config);
}

/**
 * JSON转二进制配置
 */
int station_config_json_to_binary(const station_config_json_t *json, station_config_binary_t *binary) {
    return convert_station_json_to_binary(json, binary);
}

/**
 * 二进制转JSON配置
 */
int station_config_binary_to_json(const station_config_binary_t *binary, station_config_json_t *json) {
    return convert_station_binary_to_json(binary, json);
}

/**
 * 设置默认配置
 */
void station_config_set_default(station_config_binary_t *config) {
    station_type_t type = detect_station_type();
    set_default_station_config(config, type);
}

// ==================== 兼容性函数实现 ====================

/**
 * 检测基站类型
 * 基于原有device_config.c的实现
 */
station_type_t detect_station_type(void) {
    // 简化的设备检测逻辑，实际应该根据硬件信息判断
    // 这里先返回默认基站类型
    
    // 尝试检测硬件信息
    FILE *fp = fopen("/proc/cpuinfo", "r");
    if (fp) {
        char line[256];
        while (fgets(line, sizeof(line), fp)) {
            if (strstr(line, "AM335x") || strstr(line, "am335x")) {
                fclose(fp);
                return STATION_TYPE_3G;
            }
            if (strstr(line, "ZYNQ") || strstr(line, "zynq")) {
                fclose(fp);
                return STATION_TYPE_SCI;
            }
        }
        fclose(fp);
    }
    
    // 检查是否存在特定的设备文件
    if (access("/dev/sci_device", F_OK) == 0) {
        return STATION_TYPE_SCI;
    }
    
    if (access("/dev/3g_device", F_OK) == 0) {
        return STATION_TYPE_3G;
    }
    
    // 默认返回基站类型
    return STATION_TYPE_BASE;
}

/**
 * 验证基站配置数据
 * 保持与原有device_config.c中实现完全一致
 */
int validate_station_config(const station_config_json_t *config) {
    if (!config) {
        return -1;
    }
    
    // 验证网络模式
    if (config->network_mode > 2) {
        return -1;
    }
    
    // 验证IP地址格式
    if (!validate_ip_address(config->voice_ip)) {
        return -1;
    }
    
    // 验证端口范围
    if (config->data_listen_port == 0) {
        return -1;
    }
    
    if (config->vbus_base_port == 0) {
        return -1;
    }
    
    // 验证网络地址（24位有效）
    if (config->net_address > 0xFFFFFF) {
        return -1;
    }
    
    // 验证基站类型
    if (config->base_type == 0 || config->base_type > 0x1F) {
        return -1;
    }
    
    // 验证VCAN数量
    if (config->vcan_number > 4) {
        return -1;
    }
    
    // 验证缓冲时间和掉线时间
    if (config->buffertime > 60000) {  // 最大60秒
        return -1;
    }
    
    // uint16_t类型的downtime自然限制在0-65535范围内，不需要额外验证
    // 如果需要更严格的限制，可以设置一个更小的值
    if (config->downtime == 0) {     // 掉线时间不能为0
        return -1;
    }
    
    return 0;
}

/**
 * JSON格式转换为二进制配置
 * 保持与原有device_config.c中实现完全一致
 */
int convert_station_json_to_binary(const station_config_json_t *json_config, 
                                  station_config_binary_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }
    
    // 清零结构体
    memset(binary_config, 0, sizeof(station_config_binary_t));
    
    // 转换字段
    binary_config->get_cfg_method = json_config->get_cfg_method;
    binary_config->network_mode = json_config->network_mode;
    binary_config->voice_ip = station_ip_str_to_num(json_config->voice_ip);
    binary_config->data_listen_port = json_config->data_listen_port;
    binary_config->vbus_base_port = json_config->vbus_base_port;
    binary_config->net_address = json_config->net_address & 0xFFFFFF;  // 24位字段
    binary_config->base_type = json_config->base_type;
    binary_config->vcan_number = json_config->vcan_number;
    binary_config->buffertime = json_config->buffertime;
    binary_config->downtime = json_config->downtime;
    binary_config->resettime = json_config->resettime;
    
    // 复制语音总线映射数组
    memcpy(binary_config->vbus_to_chan, json_config->vbus_to_chan, 
           sizeof(binary_config->vbus_to_chan));
    
    return 0;
}

/**
 * 二进制配置转换为JSON格式
 * 保持与原有device_config.c中实现完全一致
 */
int convert_station_binary_to_json(const station_config_binary_t *binary_config, 
                                  station_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }
    
    // 清零结构体
    memset(json_config, 0, sizeof(station_config_json_t));
    
    // 转换字段
    json_config->get_cfg_method = binary_config->get_cfg_method;
    json_config->network_mode = binary_config->network_mode;
    station_ip_num_to_str(binary_config->voice_ip, json_config->voice_ip, 
                         sizeof(json_config->voice_ip));
    json_config->data_listen_port = binary_config->data_listen_port;
    json_config->vbus_base_port = binary_config->vbus_base_port;
    json_config->net_address = binary_config->net_address;
    json_config->base_type = binary_config->base_type;
    json_config->vcan_number = binary_config->vcan_number;
    json_config->buffertime = binary_config->buffertime;
    json_config->downtime = binary_config->downtime;
    json_config->resettime = binary_config->resettime;
    
    // 复制语音总线映射数组
    memcpy(json_config->vbus_to_chan, binary_config->vbus_to_chan, 
           sizeof(json_config->vbus_to_chan));
    
    // 设置扩展字段默认值
    json_config->station_id = json_config->net_address;  // 使用网络地址作为基站ID
    snprintf(json_config->station_name, sizeof(json_config->station_name), 
             "Station_%06X", json_config->net_address);
    json_config->signal_strength = 5;  // 默认信号强度
    json_config->auto_register = 1;    // 默认自动注册
    
    return 0;
}

/**
 * 读取基站配置文件
 * 保持与原有device_config.c中实现完全一致
 */
int read_station_config_file(station_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    FILE *fp = fopen(SCICFG, "rb");
    if (!fp) {
        // 文件不存在，设置默认值
        station_type_t type = detect_station_type();
        set_default_station_config(config, type);
        return 0;
    }
    
    // 跳到指定偏移位置
    if (fseek(fp, START_ADDR_SCI, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 读取配置数据
    size_t read_size = fread(config, sizeof(station_config_binary_t), 1, fp);
    fclose(fp);
    
    if (read_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 写入基站配置文件
 * 保持与原有device_config.c中实现完全一致
 */
int write_station_config_file(const station_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    // 先尝试读取现有文件
    FILE *fp = fopen(SCICFG, "r+b");
    if (!fp) {
        // 文件不存在，创建新文件
        fp = fopen(SCICFG, "wb");
        if (!fp) {
            return -1;
        }
    }
    
    // 跳到指定偏移位置
    if (fseek(fp, START_ADDR_SCI, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 写入配置数据
    size_t write_size = fwrite(config, sizeof(station_config_binary_t), 1, fp);
    fclose(fp);
    
    if (write_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 设置基站配置默认值
 * 基于原有device_config.c的实现
 */
void set_default_station_config(station_config_binary_t *config, station_type_t station_type) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(station_config_binary_t));
    
    // 设置通用默认值
    config->get_cfg_method = 0;                          // 默认获取方法
    config->network_mode = 0;                            // 默认网络模式
    config->voice_ip = inet_addr("*************");      // 默认语音IP
    config->data_listen_port = 2600;                     // 默认数据监听端口
    config->vbus_base_port = 3000;                       // 默认语音总线基础端口
    config->net_address = 0x000001;                      // 默认网络地址
    config->base_type = station_type;                    // 设备类型
    config->vcan_number = 1;                             // 默认VCAN数量
    config->buffertime = 5000;                           // 默认缓冲时间（5秒）
    config->downtime = 30000;                            // 默认掉线时间（30秒）
    config->resettime = 10;                              // 默认重置时间（10秒）
    
    // 设置默认语音总线映射
    for (int i = 0; i < 12; i++) {
        config->vbus_to_chan[i] = i;
    }
    
    // 根据设备类型调整特定参数
    switch (station_type) {
        case STATION_TYPE_3G:
            config->data_listen_port = 2700;
            config->vbus_base_port = 3100;
            break;
        case STATION_TYPE_SCI:
            config->data_listen_port = 2600;
            config->vbus_base_port = 3000;
            break;
        default:
            // 使用通用默认值
            break;
    }
}

/**
 * 获取基站类型字符串
 * 保持与原有device_config.c中实现完全一致
 */
const char* get_station_type_string(station_type_t station_type) {
    switch (station_type) {
        case STATION_TYPE_BASE:
            return "Base Station";
        case STATION_TYPE_3G:
            return "3G Station";
        case STATION_TYPE_SCI:
            return "SCI Station";
        default:
            return "Unknown";
    }
}

// ==================== 对端配置函数 ====================

/**
 * 读取对端配置
 */
int station_peer_config_read(station_peer_config_binary_t *config, int peer_index) {
    if (!config || peer_index < 0 || peer_index >= 8) {
        return -1;
    }
    
    // 对端配置通常存储在会议配置文件中
    FILE *fp = fopen("/home/<USER>/cfg/conferece.cfg", "rb");
    if (!fp) {
        // 文件不存在，设置默认值
        station_peer_config_set_default(config, detect_station_type());
        return 0;
    }
    
    // 计算对端配置在文件中的偏移位置
    size_t offset = sizeof(station_peer_config_binary_t) * peer_index;
    if (fseek(fp, offset, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 读取对端配置数据
    size_t read_size = fread(config, sizeof(station_peer_config_binary_t), 1, fp);
    fclose(fp);
    
    if (read_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 写入对端配置
 */
int station_peer_config_write(const station_peer_config_binary_t *config, int peer_index) {
    if (!config || peer_index < 0 || peer_index >= 8) {
        return -1;
    }
    
    // 对端配置通常存储在会议配置文件中
    FILE *fp = fopen("/home/<USER>/cfg/conferece.cfg", "r+b");
    if (!fp) {
        // 文件不存在，创建新文件
        fp = fopen("/home/<USER>/cfg/conferece.cfg", "wb");
        if (!fp) {
            return -1;
        }
    }
    
    // 计算对端配置在文件中的偏移位置
    size_t offset = sizeof(station_peer_config_binary_t) * peer_index;
    if (fseek(fp, offset, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 写入对端配置数据
    size_t write_size = fwrite(config, sizeof(station_peer_config_binary_t), 1, fp);
    fclose(fp);
    
    if (write_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 设置对端配置默认值
 */
void station_peer_config_set_default(station_peer_config_binary_t *config, station_type_t station_type) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(station_peer_config_binary_t));
    
    // 设置默认值
    config->peer_ip = inet_addr("*************");       // 默认对端IP
    config->peer_voice_ip = inet_addr("*************"); // 默认对端语音IP
    config->peer_data_listen_port = 2600;               // 默认对端数据端口
    config->peer_voice_port_base = 3000;                // 默认对端语音端口基址
    config->peer_net_address = 0x000002;                // 默认对端网络地址
    config->peer_type = station_type;                   // 对端类型
    
    // 设置默认语音总线映射
    for (int i = 0; i < 12; i++) {
        config->peer_vbus_to_chan[i] = i;
    }
}

// ==================== 工具函数 ====================

/**
 * IP地址数字转字符串
 */
void station_ip_num_to_str(uint32_t ip_num, char *ip_str, size_t size) {
    if (!ip_str || size < 16) {
        return;
    }
    
    struct in_addr addr;
    addr.s_addr = ip_num;
    strncpy(ip_str, inet_ntoa(addr), size - 1);
    ip_str[size - 1] = '\0';
}

/**
 * IP地址字符串转数字
 */
uint32_t station_ip_str_to_num(const char *ip_str) {
    if (!ip_str) {
        return 0;
    }
    
    return inet_addr(ip_str);
}

// ==================== 统一配置接口实现 ====================

/**
 * 统一配置操作接口实例
 */
const config_operations_t station_config_ops = {
    .config_name = "station",
    .config_description = "基站配置",
    .json_config_size = sizeof(station_config_json_t),
    .binary_config_size = sizeof(station_config_binary_t),
    
    .validate_json = (config_validator_func_t)station_config_validate,
    .json_to_binary = (config_converter_func_t)station_config_json_to_binary,
    .binary_to_json = (config_converter_func_t)station_config_binary_to_json,
    .read_file = (config_file_func_t)station_config_read,
    .write_file = (config_file_write_func_t)station_config_write,
    .set_default = (config_default_func_t)station_config_set_default,
    
    .custom_data = NULL
}; 