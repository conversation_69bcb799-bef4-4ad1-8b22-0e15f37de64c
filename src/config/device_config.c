#include "config/device_config.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>
#include <errno.h>
#include <unistd.h>

// 占位符实现 - 第二阶段将完整实现

// TODO: 实现设备配置处理功能 

// 呼叫中心配置模块实现 - 第二阶段
/*
 * 以下呼叫中心配置相关函数已迁移到 src/config/center/center_config.c
 * 为避免重复定义，暂时注释掉这些函数
 * TODO: 在完成所有模块化重构后，完全移除这些函数
 */

#if 0  // 开始注释呼叫中心相关函数

/**
 * 验证呼叫中心配置数据
 */
int validate_center_config(const center_config_json_t *config) {
    if (!config) {
        return -1;
    }
    
    // 验证中心号码（24位有效）
    if (config->center_no > 0xFFFFFF) {
        return -1;
    }
    
    // 验证外部SSI（24位有效）
    if (config->center_outssi > 0xFFFFFF) {
        return -1;
    }
    
    // 验证内部SSI（24位有效）
    if (config->center_inssi > 0xFFFFFF) {
        return -1;
    }
    
    // 验证语音通道数（合理范围）
    if (config->vchan_sum > 64) {
        return -1;
    }
    
    // 验证端口范围（uint16_t类型无需检查上限）
    if (config->center_voice_port == 0) {
        return -1;
    }
    
    if (config->listen_agent_port == 0) {
        return -1;
    }
    
    if (config->send_to_agent_port == 0) {
        return -1;
    }
    
    // 验证对等网络类型
    if (config->peer_net_type > 2) {
        return -1;
    }
    
    // 验证IP地址格式
    struct in_addr addr;
    if (inet_aton(config->send_all_agent_ip, &addr) == 0) {
        return -1;
    }
    
    return 0;
}

/**
 * JSON格式转换为二进制配置
 */
int convert_center_json_to_binary(const center_config_json_t *json_config, 
                                center_config_binary_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }
    
    // 清零结构体
    memset(binary_config, 0, sizeof(center_config_binary_t));
    
    // 转换字段（注意24位字段的特殊处理）
    binary_config->center_no = json_config->center_no & 0xFFFFFF;
    binary_config->center_outssi = json_config->center_outssi & 0xFFFFFF;
    binary_config->center_inssi = json_config->center_inssi & 0xFFFFFF;
    binary_config->vchan_sum = json_config->vchan_sum;
    binary_config->center_voice_port = json_config->center_voice_port;
    binary_config->listen_agent_port = json_config->listen_agent_port;
    binary_config->peer_net_type = json_config->peer_net_type;
    binary_config->send_to_agent_port = json_config->send_to_agent_port;
    binary_config->inssi_num = json_config->inssi_num;
    binary_config->spec_function = json_config->spec_function;
    
    // 转换IP地址字符串为数字
    binary_config->send_all_agent_ip = ip_str_to_num(json_config->send_all_agent_ip);
    
    return 0;
}

/**
 * 二进制配置转换为JSON格式
 */
int convert_center_binary_to_json(const center_config_binary_t *binary_config, 
                                 center_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }
    
    // 清零结构体
    memset(json_config, 0, sizeof(center_config_json_t));
    
    // 转换字段
    json_config->center_no = binary_config->center_no;
    json_config->center_outssi = binary_config->center_outssi;
    json_config->center_inssi = binary_config->center_inssi;
    json_config->vchan_sum = binary_config->vchan_sum;
    json_config->center_voice_port = binary_config->center_voice_port;
    json_config->listen_agent_port = binary_config->listen_agent_port;
    json_config->peer_net_type = binary_config->peer_net_type;
    json_config->send_to_agent_port = binary_config->send_to_agent_port;
    json_config->inssi_num = binary_config->inssi_num;
    json_config->spec_function = binary_config->spec_function;
    
    // 转换IP地址数字为字符串
    ip_num_to_str(binary_config->send_all_agent_ip, json_config->send_all_agent_ip, 
                  sizeof(json_config->send_all_agent_ip));
    
    return 0;
}

/**
 * 读取呼叫中心配置文件
 */
int read_center_config_file(center_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    FILE *fp = fopen(CALLCENTERCFG, "rb");
    if (!fp) {
        // 文件不存在，设置默认值
        set_default_center_config(config);
        return 0;
    }
    
    // 跳到指定偏移位置
    if (fseek(fp, START_ADDR_CALLCENTER, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 读取配置数据
    size_t read_size = fread(config, sizeof(center_config_binary_t), 1, fp);
    fclose(fp);
    
    if (read_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 写入呼叫中心配置文件
 */
int write_center_config_file(const center_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    // 先尝试读取现有文件
    FILE *fp = fopen(CALLCENTERCFG, "r+b");
    if (!fp) {
        // 文件不存在，创建新文件
        fp = fopen(CALLCENTERCFG, "wb");
        if (!fp) {
            return -1;
        }
    }
    
    // 跳到指定偏移位置
    if (fseek(fp, START_ADDR_CALLCENTER, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 写入配置数据
    size_t write_size = fwrite(config, sizeof(center_config_binary_t), 1, fp);
    fclose(fp);
    
    if (write_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 设置呼叫中心配置默认值
 * 参考原有center.cgi的默认值设置逻辑
 */
void set_default_center_config(center_config_binary_t *config) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(center_config_binary_t));
    
    // 设置默认值（参考原有逻辑）
    config->center_no = 0x000001;           // 默认中心号码
    config->center_outssi = 0x000001;       // 默认外部SSI  
    config->center_inssi = 0x000001;        // 默认内部SSI
    config->vchan_sum = 8;                  // 默认8个语音通道
    config->center_voice_port = 3000;       // 默认语音端口起始
    config->listen_agent_port = 2800;       // 默认监听端口
    config->peer_net_type = 0;              // 默认单播
    config->send_all_agent_ip = inet_addr("*************");  // 默认广播地址
    config->send_to_agent_port = 2900;      // 默认发送端口
    config->inssi_num = 100;                // 默认内应号个数
    config->spec_function = 0;              // 默认特殊功能
}

/**
 * IP地址数字转字符串
 */
void ip_num_to_str(uint32_t ip_num, char *ip_str, size_t size) {
    if (!ip_str || size < 16) {
        return;
    }
    
    struct in_addr addr;
    addr.s_addr = ip_num;
    strncpy(ip_str, inet_ntoa(addr), size - 1);
    ip_str[size - 1] = '\0';
}

/**
 * IP地址字符串转数字
 */
uint32_t ip_str_to_num(const char *ip_str) {
    if (!ip_str) {
        return 0;
    }
    
    return inet_addr(ip_str);
}

#endif  // 结束注释呼叫中心相关函数

// 交换机配置模块实现 - 第二阶段

/**
 * 检测板卡类型
 * 通过检查硬件信息来判断板卡类型
 */
int detect_board_type(board_info_t *board_info) {
    if (!board_info) {
        return -1;
    }
    
    memset(board_info, 0, sizeof(board_info_t));
    
    FILE *fp;
    char buffer[256];
    
    // 方法1：检查PCI设备信息
    fp = fopen("/proc/bus/pci/devices", "r");
    if (fp) {
        while (fgets(buffer, sizeof(buffer), fp) != NULL) {
            // 查找特定的PCI设备ID
            if (strstr(buffer, "4G") || strstr(buffer, "LTE")) {
                fclose(fp);
                board_info->board_type = BOARD_TYPE_4G_MODULE;
                strncpy(board_info->board_name, "4G Module Board", sizeof(board_info->board_name) - 1);
                strncpy(board_info->board_version, "v2.0", sizeof(board_info->board_version) - 1);
                board_info->channel_count = 16;
                board_info->capabilities = 0x0F; // 支持语音+数据
                return 0;
            }
        }
        fclose(fp);
    }
    
    // 方法2：检查内核模块
    fp = fopen("/proc/modules", "r");
    if (fp) {
        while (fgets(buffer, sizeof(buffer), fp) != NULL) {
            if (strstr(buffer, "voice_proc") || strstr(buffer, "audio")) {
                fclose(fp);
                board_info->board_type = BOARD_TYPE_VOICE;
                strncpy(board_info->board_name, "Voice Processing Board", sizeof(board_info->board_name) - 1);
                strncpy(board_info->board_version, "v1.5", sizeof(board_info->board_version) - 1);
                board_info->channel_count = 32;
                board_info->capabilities = 0x03; // 仅支持语音
                return 0;
            }
        }
        fclose(fp);
    }
    
    // 方法3：检查网络接口（默认为IP交换机）
    fp = fopen("/proc/net/dev", "r");
    if (fp) {
        int eth_count = 0;
        while (fgets(buffer, sizeof(buffer), fp) != NULL) {
            if (strstr(buffer, "eth")) {
                eth_count++;
            }
        }
        fclose(fp);
        
        if (eth_count >= 2) {
            board_info->board_type = BOARD_TYPE_IP_SWITCH;
            strncpy(board_info->board_name, "IP Switch Board", sizeof(board_info->board_name) - 1);
            strncpy(board_info->board_version, "v3.0", sizeof(board_info->board_version) - 1);
            board_info->channel_count = 64;
            board_info->capabilities = 0x0F; // 全功能支持
            return 0;
        }
    }
    
    // 默认为未知类型
    board_info->board_type = BOARD_TYPE_UNKNOWN;
    strncpy(board_info->board_name, "Unknown Board", sizeof(board_info->board_name) - 1);
    strncpy(board_info->board_version, "v0.0", sizeof(board_info->board_version) - 1);
    board_info->channel_count = 8;
    board_info->capabilities = 0x01; // 基本功能
    
    return 0;
}

/**
 * 验证交换机配置数据
 */
int validate_switch_config(const switch_config_json_t *config) {
    if (!config) {
        return -1;
    }
    
    // 验证交换机ID（24位有效）
    if (config->switch_id > 0xFFFFFF) {
        fprintf(stderr, "Invalid switch ID: %u\n", config->switch_id);
        return -1;
    }
    
    // 验证板卡类型
    if (config->board_type >= BOARD_TYPE_MAX) {
        fprintf(stderr, "Invalid board type: %u\n", config->board_type);
        return -1;
    }
    
    // 验证端口范围
    if (config->center_port == 0) {
        fprintf(stderr, "Invalid center port: %u\n", config->center_port);
        return -1;
    }
    
    if (config->local_port == 0) {
        fprintf(stderr, "Invalid local port: %u\n", config->local_port);
        return -1;
    }
    
    // 验证通道数量（合理范围：1-128）
    if (config->channel_count == 0 || config->channel_count > 128) {
        fprintf(stderr, "Invalid channel count: %u\n", config->channel_count);
        return -1;
    }
    
    // 验证交换模式
    if (config->switch_mode > 2) {
        fprintf(stderr, "Invalid switch mode: %u\n", config->switch_mode);
        return -1;
    }
    
    // 验证优先级等级（0-7）
    if (config->priority_level > 7) {
        fprintf(stderr, "Invalid priority level: %u\n", config->priority_level);
        return -1;
    }
    
    // 验证超时间隔（合理范围：1-3600秒）
    if (config->timeout_interval == 0 || config->timeout_interval > 3600) {
        fprintf(stderr, "Invalid timeout interval: %u\n", config->timeout_interval);
        return -1;
    }
    
    // 验证交换机名称长度
    if (strlen(config->switch_name) == 0 || strlen(config->switch_name) >= 32) {
        fprintf(stderr, "Invalid switch name length\n");
        return -1;
    }
    
    return 0;
}

/**
 * JSON格式转换为二进制配置
 */
int convert_switch_json_to_binary(const switch_config_json_t *json_config, 
                                 switch_config_binary_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }
    
    // 清零结构体
    memset(binary_config, 0, sizeof(switch_config_binary_t));
    
    // 转换字段
    binary_config->switch_id = json_config->switch_id;
    binary_config->board_type = json_config->board_type;
    
    // 安全复制字符串字段
    size_t name_len = strlen(json_config->switch_name);
    if (name_len >= sizeof(binary_config->switch_name)) {
        name_len = sizeof(binary_config->switch_name) - 1;
    }
    memcpy(binary_config->switch_name, json_config->switch_name, name_len);
    binary_config->switch_name[name_len] = '\0';
    
    binary_config->center_ip = json_config->center_ip;
    binary_config->center_port = json_config->center_port;
    binary_config->local_ip = json_config->local_ip;
    binary_config->local_port = json_config->local_port;
    binary_config->channel_count = json_config->channel_count;
    binary_config->voice_start_port = json_config->voice_start_port;
    binary_config->data_start_port = json_config->data_start_port;
    binary_config->switch_mode = json_config->switch_mode;
    binary_config->timeout_interval = json_config->timeout_interval;
    binary_config->priority_level = json_config->priority_level;
    binary_config->qos_enabled = json_config->qos_enabled;
    binary_config->auto_failover = json_config->auto_failover;
    binary_config->backup_server_ip = json_config->backup_server_ip;
    binary_config->backup_server_port = json_config->backup_server_port;
    
    // 安全复制固件版本字符串
    size_t version_len = strlen(json_config->firmware_version);
    if (version_len >= sizeof(binary_config->firmware_version)) {
        version_len = sizeof(binary_config->firmware_version) - 1;
    }
    memcpy(binary_config->firmware_version, json_config->firmware_version, version_len);
    binary_config->firmware_version[version_len] = '\0';
    
    return 0;
}

/**
 * 二进制配置转换为JSON格式
 */
int convert_switch_binary_to_json(const switch_config_binary_t *binary_config, 
                                 switch_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }
    
    // 清零结构体
    memset(json_config, 0, sizeof(switch_config_json_t));
    
    // 转换字段
    json_config->switch_id = binary_config->switch_id;
    json_config->board_type = binary_config->board_type;
    
    // 安全复制字符串字段
    size_t name_len = strlen(binary_config->switch_name);
    if (name_len >= sizeof(json_config->switch_name)) {
        name_len = sizeof(json_config->switch_name) - 1;
    }
    memcpy(json_config->switch_name, binary_config->switch_name, name_len);
    json_config->switch_name[name_len] = '\0';
    
    json_config->center_ip = binary_config->center_ip;
    json_config->center_port = binary_config->center_port;
    json_config->local_ip = binary_config->local_ip;
    json_config->local_port = binary_config->local_port;
    json_config->channel_count = binary_config->channel_count;
    json_config->voice_start_port = binary_config->voice_start_port;
    json_config->data_start_port = binary_config->data_start_port;
    json_config->switch_mode = binary_config->switch_mode;
    json_config->timeout_interval = binary_config->timeout_interval;
    json_config->priority_level = binary_config->priority_level;
    json_config->qos_enabled = binary_config->qos_enabled;
    json_config->auto_failover = binary_config->auto_failover;
    json_config->backup_server_ip = binary_config->backup_server_ip;
    json_config->backup_server_port = binary_config->backup_server_port;
    
    // 安全复制固件版本字符串
    size_t version_len = strlen(binary_config->firmware_version);
    if (version_len >= sizeof(json_config->firmware_version)) {
        version_len = sizeof(json_config->firmware_version) - 1;
    }
    memcpy(json_config->firmware_version, binary_config->firmware_version, version_len);
    json_config->firmware_version[version_len] = '\0';
    
    return 0;
}

/**
 * 读取交换机配置文件
 */
int read_switch_config_file(switch_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    FILE *fp = fopen(SWITCHCFG, "rb");
    if (!fp) {
        // 文件不存在，设置默认值
        board_info_t board_info;
        detect_board_type(&board_info);
        set_default_switch_config(config, board_info.board_type);
        return 0;
    }
    
    // 跳到指定偏移位置
    if (fseek(fp, START_ADDR_SWITCH, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 读取配置数据
    size_t read_size = fread(config, sizeof(switch_config_binary_t), 1, fp);
    fclose(fp);
    
    if (read_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 写入交换机配置文件
 */
int write_switch_config_file(const switch_config_binary_t *config) {
    if (!config) {
        return -1;
    }
    
    // 先尝试读取现有文件
    FILE *fp = fopen(SWITCHCFG, "r+b");
    if (!fp) {
        // 文件不存在，创建新文件
        fp = fopen(SWITCHCFG, "wb");
        if (!fp) {
            return -1;
        }
    }
    
    // 跳到指定偏移位置
    if (fseek(fp, START_ADDR_SWITCH, SEEK_SET) != 0) {
        fclose(fp);
        return -1;
    }
    
    // 写入配置数据
    size_t write_size = fwrite(config, sizeof(switch_config_binary_t), 1, fp);
    fclose(fp);
    
    if (write_size != 1) {
        return -1;
    }
    
    return 0;
}

/**
 * 设置交换机配置默认值
 * 根据板卡类型设置不同的默认值
 */
void set_default_switch_config(switch_config_binary_t *config, board_type_t board_type) {
    if (!config) {
        return;
    }
    
    memset(config, 0, sizeof(switch_config_binary_t));
    
    // 通用默认值
    config->switch_id = 0x000001;               // 默认交换机ID
    config->board_type = board_type;            // 板卡类型
    config->center_ip = inet_addr("*************");  // 默认中心IP
    config->center_port = 5000;                 // 默认中心端口
    config->local_ip = inet_addr("*************");   // 默认本地IP
    config->local_port = 6000;                  // 默认本地端口
    config->switch_mode = 1;                    // 默认半透明模式
    config->timeout_interval = 60;             // 默认超时60秒
    config->priority_level = 3;                 // 默认优先级3
    config->qos_enabled = 1;                    // 默认开启QoS
    config->auto_failover = 1;                  // 默认开启自动故障切换
    config->backup_server_ip = inet_addr("*************");  // 默认备份服务器IP
    config->backup_server_port = 5001;         // 默认备份服务器端口
    
    // 安全设置默认名称
    const char *default_name = "Switch_001";
    size_t default_name_len = strlen(default_name);
    if (default_name_len >= sizeof(config->switch_name)) {
        default_name_len = sizeof(config->switch_name) - 1;
    }
    memcpy(config->switch_name, default_name, default_name_len);
    config->switch_name[default_name_len] = '\0';
    
    // 安全设置默认固件版本
    const char *default_version = "v2.1.0";
    size_t default_version_len = strlen(default_version);
    if (default_version_len >= sizeof(config->firmware_version)) {
        default_version_len = sizeof(config->firmware_version) - 1;
    }
    memcpy(config->firmware_version, default_version, default_version_len);
    config->firmware_version[default_version_len] = '\0';
    
    // 根据板卡类型设置特定默认值
    switch (board_type) {
        case BOARD_TYPE_IP_SWITCH:
            config->channel_count = 64;         // IP交换机64通道
            config->voice_start_port = 7000;    // 语音起始端口
            config->data_start_port = 8000;     // 数据起始端口
            break;
            
        case BOARD_TYPE_4G_MODULE:
            config->channel_count = 16;         // 4G模块16通道
            config->voice_start_port = 7100;    // 语音起始端口
            config->data_start_port = 8100;     // 数据起始端口
            break;
            
        case BOARD_TYPE_VOICE:
            config->channel_count = 32;         // 语音板32通道
            config->voice_start_port = 7200;    // 语音起始端口
            config->data_start_port = 8200;     // 数据起始端口
            break;
            
        default:
            config->channel_count = 8;          // 默认8通道
            config->voice_start_port = 7300;    // 语音起始端口
            config->data_start_port = 8300;     // 数据起始端口
            break;
    }
}

/**
 * 获取板卡类型字符串
 */
const char* get_board_type_string(board_type_t board_type) {
    switch (board_type) {
        case BOARD_TYPE_IP_SWITCH:
            return "IP Switch";
        case BOARD_TYPE_4G_MODULE:
            return "4G Module";
        case BOARD_TYPE_VOICE:
            return "Voice Processing";
        default:
            return "Unknown";
    }
}

/**
 * 获取交换模式字符串
 */
const char* get_switch_mode_string(uint8_t switch_mode) {
    switch (switch_mode) {
        case 0:
            return "透明模式";
        case 1:
            return "半透明模式";
        case 2:
            return "不透明模式";
        default:
            return "未知模式";
    }
}

/*
 * 以下基站配置相关函数已迁移到 src/config/station/station_config.c
 * 为避免重复定义，暂时注释掉这些函数
 * TODO: 在完成所有模块化重构后，完全移除这些函数
 */

#if 0  // 开始注释基站相关函数

station_type_t detect_station_type(void) {
    // 已迁移到 station_config.c
}

int validate_station_config(const station_config_json_t *config) {
    // 已迁移到 station_config.c
}

int convert_station_json_to_binary(const station_config_json_t *json_config, 
                                 station_config_binary_t *binary_config) {
    // 已迁移到 station_config.c
}

int convert_station_binary_to_json(const station_config_binary_t *binary_config, 
                                 station_config_json_t *json_config) {
    // 已迁移到 station_config.c
}

int read_station_config_file(station_config_binary_t *config) {
    // 已迁移到 station_config.c
}

int write_station_config_file(const station_config_binary_t *config) {
    // 已迁移到 station_config.c
}

void set_default_station_config(station_config_binary_t *config, station_type_t station_type) {
    // 已迁移到 station_config.c
}

const char* get_station_type_string(station_type_t station_type) {
    // 已迁移到 station_config.c
}

#endif  // 结束注释基站相关函数 