/*******************************************************************
 * File          : recorder_base_config.c
 * Author        : AI Assistant
 * Created       : 2024-12-26
 * Last modified : 2024-12-26
 *------------------------------------------------------------------
 * Description :
 * 录音基站配置模块实现 - 基于旧项目0mini.c和0recorder.c的真实逻辑
 * 支持录音模块和最小基站两种设备类型
 *------------------------------------------------------------------
 * Modification history :
 * 2024-12-26 : 基于旧项目实际结构重新创建
 *******************************************************************/

#include "config/recorder_base_config.h"
#include <string.h>
#include <stdlib.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <sys/stat.h>
#include <errno.h>
#include <regex.h>

// 配置文件路径 - 基于旧项目0define.h
#define COMMONCFG       "/home/<USER>/cfg/common.cfg"
#define BOARDCFG        "/home/<USER>/cfg/board.cfg"
#define CONFERENCECFG   "/home/<USER>/cfg/conferece.cfg"
#define ETHERNETCFG     "/etc/eth0-setting"

// 配置文件长度和地址定义
#define FILE_LEN_OF_COMMONCFG    4096
#define FILE_LEN_OF_BOARDCFG     4096
#define FILE_LEN_OF_CONFIGURECFG 4096

#define START_ADDR_COMMONCFG     0
#define START_ADDR_BOARD_BASIC   256
#define START_ADDR_CONF          512
#define START_ADDR_CONF_PEER     768
#define LEN_RESV_CONF_PEER       64

#define PEER_MAX                 8

// 错误标志位
typedef enum {
    READ_SUCCESS = 0,
    SAVE_SUCCESS = 1,
    OPEN_ETH_ERR = (1 << 1),
    OPEN_BASIC_ERR = (1 << 11),
    OPEN_COMMON_ERR = (1 << 14),
    OPEN_CONF_ERR = (1 << 15)
} check_ret_t;

// 工具函数声明
static int create_cfg_file(const char *filename, int file_len);
static int read_configure(uint8_t type, uint32_t addr, uint32_t len, void *data);
static int write_configure(uint8_t type, uint32_t addr, uint32_t len, const void *data);
static int read_eth_config(st_board_net_t *pBoard, int nomac);
static int write_eth_config(st_board_net_t *pBoard, int nomac);
static void eth_str_to_num(const st_net_config_t *pConfig, st_cfg_net_t *pNet);
static void eth_num_to_str(st_net_config_t *pConfig, const st_cfg_net_t *pNet);
static void set_default_value_boardnet(st_board_net_t *pBoard);
static void set_default_value_commoncfg(st_cfg_common_t *pCommon);
static void set_default_value_boardcfg(st_cfg_board_basic_t *pBoardBasic);
static void set_default_value_conference(st_cfg_conf_t *pConf);
static void set_default_value_peerbase(st_cfg_peer_base_t *pPeer, uint8_t type);

/**
 * 读取录音基站配置 - 基于旧项目mini_read_cfg函数
 */
int read_recorder_base_config(recorder_base_config_t *config, recorder_base_device_type_t device_type) {
    if (!config) {
        return -1;
    }

    int ret;
    int checkret = READ_SUCCESS;
    int peer_address;

    // 设置设备类型
    config->device_type = device_type;

    // 读取网络配置
    ret = read_eth_config(&config->board_net, 0);
    if (ret == -1) {
        checkret |= OPEN_ETH_ERR;
        set_default_value_boardnet(&config->board_net);
    }
    eth_str_to_num(&config->board_net.ethconfig, &config->net_config);

    // 读取通用配置
    ret = read_configure(0x10, START_ADDR_COMMONCFG, sizeof(st_cfg_common_t), &config->common_config);
    if (ret < 0) {
        checkret |= OPEN_COMMON_ERR;
        set_default_value_commoncfg(&config->common_config);
        create_cfg_file(COMMONCFG, FILE_LEN_OF_COMMONCFG);
        write_configure(0x10, START_ADDR_COMMONCFG, sizeof(st_cfg_common_t), &config->common_config);
    }

    // 读取板卡基础配置
    ret = read_configure(0x10, START_ADDR_BOARD_BASIC, sizeof(st_cfg_board_basic_t), &config->board_basic);
    if (ret < 0) {
        checkret |= OPEN_BASIC_ERR;
        set_default_value_boardcfg(&config->board_basic);
        create_cfg_file(BOARDCFG, FILE_LEN_OF_BOARDCFG);
        write_configure(0x10, START_ADDR_BOARD_BASIC, sizeof(st_cfg_board_basic_t), &config->board_basic);
    }

    // 读取会议配置
    ret = read_configure(0x10, START_ADDR_CONF, sizeof(st_cfg_conf_t), &config->conference_config);
    if (ret < 0) {
        checkret |= OPEN_CONF_ERR;
        set_default_value_conference(&config->conference_config);
        set_default_value_peerbase(&config->peer_configs[0], device_type);
        create_cfg_file(CONFERENCECFG, FILE_LEN_OF_CONFIGURECFG);
        write_configure(0x10, START_ADDR_CONF, sizeof(st_cfg_conf_t), &config->conference_config);
        write_configure(0x10, START_ADDR_CONF_PEER, sizeof(st_cfg_peer_base_t), &config->peer_configs[0]);
    }

    // 读取对端配置
    peer_address = START_ADDR_CONF_PEER;
    for (int i = 0; i < PEER_MAX; i++) {
        ret = read_configure(0x10, peer_address, sizeof(st_cfg_peer_base_t), &config->peer_configs[i]);
        if (ret < 0) {
            checkret |= OPEN_CONF_ERR;
            set_default_value_peerbase(&config->peer_configs[i], device_type);
        }
        peer_address += LEN_RESV_CONF_PEER;
    }

    return checkret;
}

/**
 * 写入录音基站配置 - 基于旧项目mini_write_cfg函数
 */
int write_recorder_base_config(const recorder_base_config_t *config, recorder_base_device_type_t device_type) {
    if (!config) {
        return -1;
    }

    int ret;
    int checkret = READ_SUCCESS;
    int peer_address;

    // 创建可修改的配置副本
    recorder_base_config_t temp_config = *config;

    // 写入网络配置
    ret = write_eth_config(&temp_config.board_net, 0);
    if (ret < 0) {
        checkret |= OPEN_ETH_ERR;
    }
    eth_str_to_num(&temp_config.board_net.ethconfig, &temp_config.net_config);

    // 写入通用配置
    ret = write_configure(0x10, START_ADDR_COMMONCFG, sizeof(st_cfg_common_t), &temp_config.common_config);
    if (ret < 0) {
        checkret |= OPEN_BASIC_ERR;
    }

    // 写入板卡基础配置
    ret = write_configure(0x10, START_ADDR_BOARD_BASIC, sizeof(st_cfg_board_basic_t), &temp_config.board_basic);
    if (ret < 0) {
        checkret |= OPEN_BASIC_ERR;
    }

    // 设置会议配置默认参数
    temp_config.conference_config.voice_ip = temp_config.net_config.ip;
    temp_config.conference_config.peer_base_num = 1;
    temp_config.conference_config.vbus_base_port = 5000;

    // 写入会议配置
    ret = write_configure(0x10, START_ADDR_CONF, sizeof(st_cfg_conf_t), &temp_config.conference_config);
    if (ret < 0) {
        checkret |= OPEN_CONF_ERR;
    }

    // 写入对端配置
    peer_address = START_ADDR_CONF_PEER;
    for (int i = 0; i < PEER_MAX; i++) {
        // 设置对端基站参数
        temp_config.peer_configs[i].peer_voice_ip = temp_config.peer_configs[i].peer_ip;
        temp_config.peer_configs[i].peer_voice_port_base = temp_config.peer_configs[i].peer_data_listen_port;
        temp_config.peer_configs[i].peer_type = device_type;

        ret = write_configure(0x10, peer_address, sizeof(st_cfg_peer_base_t), &temp_config.peer_configs[i]);
        if (ret != 0) {
            checkret |= OPEN_CONF_ERR;
        }
        peer_address += LEN_RESV_CONF_PEER;
    }

    return checkret;
}

/**
 * 验证录音基站配置
 */
int validate_recorder_base_config(const recorder_base_config_json_t *config) {
    if (!config) {
        return -1;
    }

    // 验证IP地址格式
    struct sockaddr_in sa;
    if (inet_pton(AF_INET, config->ip, &(sa.sin_addr)) != 1) {
        return -1;
    }
    if (inet_pton(AF_INET, config->mask, &(sa.sin_addr)) != 1) {
        return -1;
    }
    if (inet_pton(AF_INET, config->gateway, &(sa.sin_addr)) != 1) {
        return -1;
    }
    if (inet_pton(AF_INET, config->dns, &(sa.sin_addr)) != 1) {
        return -1;
    }

    // 验证MAC地址格式
    regex_t regex;
    int reti = regcomp(&regex, "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", REG_EXTENDED);
    if (reti) {
        return -1;
    }
    reti = regexec(&regex, config->mac, 0, NULL, 0);
    regfree(&regex);
    if (reti == REG_NOMATCH) {
        return -1;
    }

    // 验证设备类型
    if (config->device_type != DEVICE_TYPE_RECORDER && config->device_type != DEVICE_TYPE_MINI) {
        return -1;
    }

    // 验证端口范围（uint16_t类型自动限制在0-65535范围内）
    if (config->daemon_port == 0) {
        return -1;
    }
    if (config->log_port == 0) {
        return -1;
    }
    if (config->cfg_port == 0) {
        return -1;
    }

    return 0;
}

/**
 * JSON配置转换为二进制配置
 */
int convert_recorder_base_json_to_binary(const recorder_base_config_json_t *json_config,
                                       recorder_base_config_t *binary_config) {
    if (!json_config || !binary_config) {
        return -1;
    }

    memset(binary_config, 0, sizeof(recorder_base_config_t));

    // 转换网络配置
    strncpy(binary_config->board_net.ethconfig.ip, json_config->ip, sizeof(binary_config->board_net.ethconfig.ip) - 1);
    strncpy(binary_config->board_net.ethconfig.mask, json_config->mask, sizeof(binary_config->board_net.ethconfig.mask) - 1);
    strncpy(binary_config->board_net.ethconfig.gateway, json_config->gateway, sizeof(binary_config->board_net.ethconfig.gateway) - 1);
    strncpy(binary_config->board_net.ethconfig.dns, json_config->dns, sizeof(binary_config->board_net.ethconfig.dns) - 1);
    strncpy(binary_config->board_net.ethconfig.mac, json_config->mac, sizeof(binary_config->board_net.ethconfig.mac) - 1);

    // 转换二进制网络配置
    eth_str_to_num(&binary_config->board_net.ethconfig, &binary_config->net_config);

    // 转换通用配置
    binary_config->common_config.ds = json_config->ds;
    binary_config->common_config.sw = json_config->sw;
    binary_config->common_config.conf_num = json_config->conf_num;
    binary_config->common_config.normal_num = json_config->normal_num;

    // 转换板卡基础配置
    inet_pton(AF_INET, json_config->daemon_ip, &binary_config->board_basic.daemon_ip);
    binary_config->board_basic.daemon_port = json_config->daemon_port;
    inet_pton(AF_INET, json_config->log_ip, &binary_config->board_basic.log_ip);
    binary_config->board_basic.log_port = json_config->log_port;
    inet_pton(AF_INET, json_config->cfg_ip, &binary_config->board_basic.cfg_ip);
    binary_config->board_basic.cfg_port = json_config->cfg_port;
    binary_config->board_basic.log_level = json_config->log_level;
    binary_config->board_basic.log_to_where = json_config->log_to_where;
    binary_config->board_basic.data_listen_port = json_config->data_listen_port;
    binary_config->board_basic.data_send_port = json_config->data_send_port;

    // 转换会议配置
    binary_config->conference_config.work_mode = json_config->work_mode;
    binary_config->conference_config.spec_function = json_config->spec_function;
    binary_config->conference_config.peer_base_num = json_config->peer_base_num;
    inet_pton(AF_INET, json_config->voice_ip, &binary_config->conference_config.voice_ip);
    binary_config->conference_config.vbus_base_port = json_config->vbus_base_port;
    binary_config->conference_config.vchan_number = json_config->vchan_number;
    binary_config->conference_config.buffertime = json_config->buffertime;
    binary_config->conference_config.downtime = json_config->downtime;

    // 转换对端配置
    for (int i = 0; i < 8; i++) {
        inet_pton(AF_INET, json_config->peer_configs[i].peer_ip, &binary_config->peer_configs[i].peer_ip);
        inet_pton(AF_INET, json_config->peer_configs[i].peer_voice_ip, &binary_config->peer_configs[i].peer_voice_ip);
        binary_config->peer_configs[i].peer_data_listen_port = json_config->peer_configs[i].peer_data_listen_port;
        binary_config->peer_configs[i].peer_voice_port_base = json_config->peer_configs[i].peer_voice_port_base;
        binary_config->peer_configs[i].peer_net_address = json_config->peer_configs[i].peer_net_address & 0xFFFFFF;
        binary_config->peer_configs[i].peer_type = json_config->peer_configs[i].peer_type;
        memcpy(binary_config->peer_configs[i].peer_vbus_to_chan, json_config->peer_configs[i].peer_vbus_to_chan, 12);
    }

    // 设置设备类型
    binary_config->device_type = (recorder_base_device_type_t)json_config->device_type;

    return 0;
}

/**
 * 二进制配置转换为JSON配置
 */
int convert_recorder_base_binary_to_json(const recorder_base_config_t *binary_config,
                                       recorder_base_config_json_t *json_config) {
    if (!binary_config || !json_config) {
        return -1;
    }

    memset(json_config, 0, sizeof(recorder_base_config_json_t));

    // 转换网络配置
    strncpy(json_config->ip, binary_config->board_net.ethconfig.ip, sizeof(json_config->ip) - 1);
    strncpy(json_config->mask, binary_config->board_net.ethconfig.mask, sizeof(json_config->mask) - 1);
    strncpy(json_config->gateway, binary_config->board_net.ethconfig.gateway, sizeof(json_config->gateway) - 1);
    strncpy(json_config->dns, binary_config->board_net.ethconfig.dns, sizeof(json_config->dns) - 1);
    strncpy(json_config->mac, binary_config->board_net.ethconfig.mac, sizeof(json_config->mac) - 1);

    // 转换通用配置
    json_config->ds = binary_config->common_config.ds;
    json_config->sw = binary_config->common_config.sw;
    json_config->conf_num = binary_config->common_config.conf_num;
    json_config->normal_num = binary_config->common_config.normal_num;

    // 转换板卡基础配置
    inet_ntop(AF_INET, &binary_config->board_basic.daemon_ip, json_config->daemon_ip, sizeof(json_config->daemon_ip));
    json_config->daemon_port = binary_config->board_basic.daemon_port;
    inet_ntop(AF_INET, &binary_config->board_basic.log_ip, json_config->log_ip, sizeof(json_config->log_ip));
    json_config->log_port = binary_config->board_basic.log_port;
    inet_ntop(AF_INET, &binary_config->board_basic.cfg_ip, json_config->cfg_ip, sizeof(json_config->cfg_ip));
    json_config->cfg_port = binary_config->board_basic.cfg_port;
    json_config->log_level = binary_config->board_basic.log_level;
    json_config->log_to_where = binary_config->board_basic.log_to_where;
    json_config->data_listen_port = binary_config->board_basic.data_listen_port;
    json_config->data_send_port = binary_config->board_basic.data_send_port;

    // 转换会议配置
    json_config->work_mode = binary_config->conference_config.work_mode;
    json_config->spec_function = binary_config->conference_config.spec_function;
    json_config->peer_base_num = binary_config->conference_config.peer_base_num;
    inet_ntop(AF_INET, &binary_config->conference_config.voice_ip, json_config->voice_ip, sizeof(json_config->voice_ip));
    json_config->vbus_base_port = binary_config->conference_config.vbus_base_port;
    json_config->vchan_number = binary_config->conference_config.vchan_number;
    json_config->buffertime = binary_config->conference_config.buffertime;
    json_config->downtime = binary_config->conference_config.downtime;

    // 转换对端配置
    for (int i = 0; i < 8; i++) {
        inet_ntop(AF_INET, &binary_config->peer_configs[i].peer_ip, json_config->peer_configs[i].peer_ip, sizeof(json_config->peer_configs[i].peer_ip));
        inet_ntop(AF_INET, &binary_config->peer_configs[i].peer_voice_ip, json_config->peer_configs[i].peer_voice_ip, sizeof(json_config->peer_configs[i].peer_voice_ip));
        json_config->peer_configs[i].peer_data_listen_port = binary_config->peer_configs[i].peer_data_listen_port;
        json_config->peer_configs[i].peer_voice_port_base = binary_config->peer_configs[i].peer_voice_port_base;
        json_config->peer_configs[i].peer_net_address = binary_config->peer_configs[i].peer_net_address;
        json_config->peer_configs[i].peer_type = binary_config->peer_configs[i].peer_type;
        memcpy(json_config->peer_configs[i].peer_vbus_to_chan, binary_config->peer_configs[i].peer_vbus_to_chan, 12);
    }

    // 设置设备信息
    json_config->device_type = (uint8_t)binary_config->device_type;
    strncpy(json_config->device_name, get_recorder_base_device_name(binary_config->device_type), sizeof(json_config->device_name) - 1);

    return 0;
}

/**
 * 设置默认配置
 */
void set_default_recorder_base_config(recorder_base_config_t *config, recorder_base_device_type_t device_type) {
    if (!config) {
        return;
    }

    memset(config, 0, sizeof(recorder_base_config_t));

    // 设置设备类型
    config->device_type = device_type;

    // 设置默认网络配置
    set_default_value_boardnet(&config->board_net);
    eth_str_to_num(&config->board_net.ethconfig, &config->net_config);

    // 设置默认通用配置
    set_default_value_commoncfg(&config->common_config);

    // 设置默认板卡配置
    set_default_value_boardcfg(&config->board_basic);

    // 设置默认会议配置
    set_default_value_conference(&config->conference_config);

    // 设置默认对端配置
    for (int i = 0; i < PEER_MAX; i++) {
        set_default_value_peerbase(&config->peer_configs[i], device_type);
    }
}

/**
 * 检测设备类型
 */
recorder_base_device_type_t detect_recorder_base_device_type(void) {
    // 检查设备特征文件或硬件标识
    if (access("/sys/class/audio", F_OK) == 0) {
        return DEVICE_TYPE_RECORDER;
    } else {
        return DEVICE_TYPE_MINI;
    }
}

/**
 * 获取设备类型名称
 */
const char* get_recorder_base_device_name(recorder_base_device_type_t device_type) {
    switch (device_type) {
        case DEVICE_TYPE_RECORDER:
            return "录音模块";
        case DEVICE_TYPE_MINI:
            return "最小基站";
        default:
            return "未知设备";
    }
}

// ========== 工具函数实现 ==========

static int create_cfg_file(const char *filename, int file_len) {
    FILE *fp = fopen(filename, "wb");
    if (!fp) {
        return -1;
    }

    // 创建指定长度的空文件
    fseek(fp, file_len - 1, SEEK_SET);
    fputc(0, fp);
    fclose(fp);
    return 0;
}

static int read_configure(uint8_t type, uint32_t addr, uint32_t len, void *data) {
    // 简化的配置读取实现
    const char *filename = NULL;
    
    // 根据类型选择配置文件
    if (addr < START_ADDR_BOARD_BASIC) {
        filename = COMMONCFG;
    } else if (addr >= START_ADDR_BOARD_BASIC && addr < START_ADDR_CONF) {
        filename = BOARDCFG;
    } else if (addr >= START_ADDR_CONF) {
        filename = CONFERENCECFG;
    }

    if (!filename) {
        return -1;
    }

    FILE *fp = fopen(filename, "rb");
    if (!fp) {
        return -1;
    }

    fseek(fp, addr % 512, SEEK_SET);  // 简化的地址映射
    size_t read_len = fread(data, 1, len, fp);
    fclose(fp);

    return (read_len == len) ? 0 : -1;
}

static int write_configure(uint8_t type, uint32_t addr, uint32_t len, const void *data) {
    // 简化的配置写入实现
    const char *filename = NULL;
    
    // 根据类型选择配置文件
    if (addr < START_ADDR_BOARD_BASIC) {
        filename = COMMONCFG;
    } else if (addr >= START_ADDR_BOARD_BASIC && addr < START_ADDR_CONF) {
        filename = BOARDCFG;
    } else if (addr >= START_ADDR_CONF) {
        filename = CONFERENCECFG;
    }

    if (!filename) {
        return -1;
    }

    FILE *fp = fopen(filename, "r+b");
    if (!fp) {
        fp = fopen(filename, "wb");
        if (!fp) {
            return -1;
        }
    }

    fseek(fp, addr % 512, SEEK_SET);  // 简化的地址映射
    size_t write_len = fwrite(data, 1, len, fp);
    fclose(fp);

    return (write_len == len) ? 0 : -1;
}

static int read_eth_config(st_board_net_t *pBoard, int nomac) {
    if (!pBoard) {
        return -1;
    }

    FILE *fp = fopen(ETHERNETCFG, "r");
    if (!fp) {
        return -1;
    }

    char line[256];
    while (fgets(line, sizeof(line), fp)) {
        char key[64], value[64];
        if (sscanf(line, "%63[^=]=%63s", key, value) == 2) {
            if (strcmp(key, "IP") == 0) {
                strncpy(pBoard->ethconfig.ip, value, sizeof(pBoard->ethconfig.ip) - 1);
            } else if (strcmp(key, "Mask") == 0) {
                strncpy(pBoard->ethconfig.mask, value, sizeof(pBoard->ethconfig.mask) - 1);
            } else if (strcmp(key, "Gateway") == 0) {
                strncpy(pBoard->ethconfig.gateway, value, sizeof(pBoard->ethconfig.gateway) - 1);
            } else if (strcmp(key, "DNS") == 0) {
                strncpy(pBoard->ethconfig.dns, value, sizeof(pBoard->ethconfig.dns) - 1);
            } else if (strcmp(key, "MAC") == 0 && !nomac) {
                strncpy(pBoard->ethconfig.mac, value, sizeof(pBoard->ethconfig.mac) - 1);
            }
        }
    }

    fclose(fp);
    return 0;
}

static int write_eth_config(st_board_net_t *pBoard, int nomac) {
    if (!pBoard) {
        return -1;
    }

    FILE *fp = fopen(ETHERNETCFG, "w");
    if (!fp) {
        return -1;
    }

    fprintf(fp, "IP=%s\n", pBoard->ethconfig.ip);
    fprintf(fp, "Mask=%s\n", pBoard->ethconfig.mask);
    fprintf(fp, "Gateway=%s\n", pBoard->ethconfig.gateway);
    fprintf(fp, "DNS=%s\n", pBoard->ethconfig.dns);
    if (!nomac) {
        fprintf(fp, "MAC=%s\n", pBoard->ethconfig.mac);
    }

    fclose(fp);
    return 0;
}

static void eth_str_to_num(const st_net_config_t *pConfig, st_cfg_net_t *pNet) {
    if (!pConfig || !pNet) {
        return;
    }

    inet_pton(AF_INET, pConfig->ip, &pNet->ip);
    inet_pton(AF_INET, pConfig->mask, &pNet->mask);
    inet_pton(AF_INET, pConfig->gateway, &pNet->gateway);
    inet_pton(AF_INET, pConfig->dns, &pNet->dns);

    // 转换MAC地址
    unsigned int mac_bytes[6];
    sscanf(pConfig->mac, "%02x:%02x:%02x:%02x:%02x:%02x",
           &mac_bytes[0], &mac_bytes[1], &mac_bytes[2],
           &mac_bytes[3], &mac_bytes[4], &mac_bytes[5]);
    for (int i = 0; i < 6; i++) {
        pNet->mac[i] = (uint8_t)mac_bytes[i];
    }
}

__attribute__((unused)) static void eth_num_to_str(st_net_config_t *pConfig, const st_cfg_net_t *pNet) {
    if (!pConfig || !pNet) {
        return;
    }

    inet_ntop(AF_INET, &pNet->ip, pConfig->ip, sizeof(pConfig->ip));
    inet_ntop(AF_INET, &pNet->mask, pConfig->mask, sizeof(pConfig->mask));
    inet_ntop(AF_INET, &pNet->gateway, pConfig->gateway, sizeof(pConfig->gateway));
    inet_ntop(AF_INET, &pNet->dns, pConfig->dns, sizeof(pConfig->dns));

    // 转换MAC地址
    snprintf(pConfig->mac, sizeof(pConfig->mac), "%02x:%02x:%02x:%02x:%02x:%02x",
             pNet->mac[0], pNet->mac[1], pNet->mac[2],
             pNet->mac[3], pNet->mac[4], pNet->mac[5]);
}

static void set_default_value_boardnet(st_board_net_t *pBoard) {
    if (!pBoard) {
        return;
    }

    strcpy(pBoard->ethconfig.ip, "***********00");
    strcpy(pBoard->ethconfig.mask, "*************");
    strcpy(pBoard->ethconfig.gateway, "***********");
    strcpy(pBoard->ethconfig.dns, "*******");
    strcpy(pBoard->ethconfig.mac, "00:11:22:33:44:55");
    pBoard->wlan_enable_val = 0;
}

static void set_default_value_commoncfg(st_cfg_common_t *pCommon) {
    if (!pCommon) {
        return;
    }

    pCommon->ds = 1;
    pCommon->sw = 1;
    pCommon->conf_num = 8;
    pCommon->normal_num = 8;
}

static void set_default_value_boardcfg(st_cfg_board_basic_t *pBoardBasic) {
    if (!pBoardBasic) {
        return;
    }

    inet_pton(AF_INET, "***********", &pBoardBasic->daemon_ip);
    pBoardBasic->daemon_port = 8080;
    inet_pton(AF_INET, "***********", &pBoardBasic->log_ip);
    pBoardBasic->log_port = 514;
    inet_pton(AF_INET, "***********", &pBoardBasic->cfg_ip);
    pBoardBasic->cfg_port = 8081;
    pBoardBasic->log_level = 6;
    pBoardBasic->log_to_where = 2;
    pBoardBasic->data_listen_port = 2600;
    pBoardBasic->data_send_port = 2601;
}

static void set_default_value_conference(st_cfg_conf_t *pConf) {
    if (!pConf) {
        return;
    }

    pConf->work_mode = 0;
    pConf->spec_function = 0;
    pConf->peer_base_num = 1;
    inet_pton(AF_INET, "***********00", &pConf->voice_ip);
    pConf->vbus_base_port = 5000;
    pConf->vchan_number = 8;
    pConf->buffertime = 100;
    pConf->downtime = 3000;
}

static void set_default_value_peerbase(st_cfg_peer_base_t *pPeer, uint8_t type) {
    if (!pPeer) {
        return;
    }

    inet_pton(AF_INET, "***********", &pPeer->peer_ip);
    inet_pton(AF_INET, "***********", &pPeer->peer_voice_ip);
    pPeer->peer_data_listen_port = 2600;
    pPeer->peer_voice_port_base = 5000;
    pPeer->peer_net_address = 0x010101;  // 1.1.1的24位地址
    pPeer->peer_type = type;
    memset(pPeer->peer_vbus_to_chan, 0, 12);
} 