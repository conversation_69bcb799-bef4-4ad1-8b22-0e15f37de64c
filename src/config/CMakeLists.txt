# config模块CMakeLists.txt

set(CONFIG_SOURCES
    config_manager.c
    network_config.c
    device_config.c
    binary_config.c
)

set(CONFIG_HEADERS
    ${CMAKE_SOURCE_DIR}/include/config/config_manager.h
    ${CMAKE_SOURCE_DIR}/include/config/network_config.h
    ${CMAKE_SOURCE_DIR}/include/config/device_config.h
    ${CMAKE_SOURCE_DIR}/include/config/binary_config.h
)

# 创建静态库
add_library(webcfg-config STATIC ${CONFIG_SOURCES})

# 包含目录
target_include_directories(webcfg-config
    PUBLIC ${CMAKE_SOURCE_DIR}/include
    PRIVATE ${CMAKE_SOURCE_DIR}/include/config
)

# 链接依赖
target_link_libraries(webcfg-config
    webcfg-utils
    cjson
) 