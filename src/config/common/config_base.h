#ifndef CONFIG_COMMON_CONFIG_BASE_H
#define CONFIG_COMMON_CONFIG_BASE_H

#include <stddef.h>
#include <stdint.h>
#include <cJSON.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file config_base.h
 * @brief 配置模块基础框架
 * <AUTHOR> Assistant
 * @date 2024-12-26
 * 
 * 提供统一的配置操作接口，用于所有设备配置模块
 */

// 前向声明
struct MHD_Connection;

/**
 * @brief 配置验证函数类型
 * @param json_config JSON格式的配置数据
 * @return 0表示验证成功，-1表示验证失败
 */
typedef int (*config_validator_func_t)(const void *json_config);

/**
 * @brief 配置转换函数类型
 * @param input 输入数据
 * @param output 输出数据
 * @return 0表示转换成功，-1表示转换失败
 */
typedef int (*config_converter_func_t)(const void *input, void *output);

/**
 * @brief 配置文件读写函数类型
 * @param config 配置数据
 * @return 0表示操作成功，-1表示操作失败
 */
typedef int (*config_file_func_t)(void *config);

/**
 * @brief 配置文件写入函数类型
 * @param config 配置数据
 * @return 0表示操作成功，-1表示操作失败
 */
typedef int (*config_file_write_func_t)(const void *config);

/**
 * @brief 配置默认值设置函数类型
 * @param config 配置数据
 */
typedef void (*config_default_func_t)(void *config);

/**
 * @brief 配置操作接口结构体
 * 
 * 所有设备配置模块都需要实现这个接口，提供统一的配置操作方式
 */
typedef struct {
    const char *config_name;                    // 配置名称
    const char *config_description;             // 配置描述
    size_t json_config_size;                    // JSON配置结构大小
    size_t binary_config_size;                  // 二进制配置结构大小
    
    // 验证函数
    config_validator_func_t validate_json;      // JSON配置验证
    
    // 转换函数
    config_converter_func_t json_to_binary;     // JSON → 二进制转换
    config_converter_func_t binary_to_json;     // 二进制 → JSON转换
    
    // 文件操作函数
    config_file_func_t read_file;               // 读取配置文件
    config_file_write_func_t write_file;        // 写入配置文件
    
    // 默认值设置
    config_default_func_t set_default;          // 设置默认配置
    
    // 可选的自定义处理函数
    void *custom_data;                          // 自定义数据指针
} config_operations_t;

/**
 * @brief 配置处理结果枚举
 */
typedef enum {
    CONFIG_RESULT_SUCCESS = 0,          // 操作成功
    CONFIG_RESULT_INVALID_PARAM = -1,   // 参数无效
    CONFIG_RESULT_VALIDATION_FAILED = -2, // 验证失败
    CONFIG_RESULT_CONVERSION_FAILED = -3, // 转换失败
    CONFIG_RESULT_FILE_ERROR = -4,      // 文件操作错误
    CONFIG_RESULT_MEMORY_ERROR = -5,    // 内存错误
    CONFIG_RESULT_JSON_ERROR = -6       // JSON处理错误
} config_result_t;

/**
 * @brief 配置响应数据结构
 */
typedef struct {
    config_result_t result;             // 操作结果
    char *message;                      // 响应消息
    cJSON *data;                        // 响应数据
    long timestamp;                     // 时间戳
} config_response_t;

// ==================== 通用配置处理接口 ====================

/**
 * @brief 处理配置获取请求
 * @param ops 配置操作接口
 * @param response 输出的响应数据
 * @return config_result_t 操作结果
 */
config_result_t config_process_get_request(const config_operations_t *ops, 
                                          config_response_t *response);

/**
 * @brief 处理配置保存请求
 * @param ops 配置操作接口
 * @param request_json 输入的JSON请求数据
 * @param response 输出的响应数据
 * @return config_result_t 操作结果
 */
config_result_t config_process_post_request(const config_operations_t *ops, 
                                           const cJSON *request_json,
                                           config_response_t *response);

/**
 * @brief 验证配置数据
 * @param ops 配置操作接口
 * @param json_config JSON格式的配置数据
 * @return config_result_t 验证结果
 */
config_result_t config_validate_data(const config_operations_t *ops,
                                    const void *json_config);

/**
 * @brief 获取配置默认值
 * @param ops 配置操作接口
 * @param response 输出的响应数据（包含默认配置）
 * @return config_result_t 操作结果
 */
config_result_t config_get_default(const config_operations_t *ops,
                                  config_response_t *response);

// ==================== 响应处理接口 ====================

/**
 * @brief 创建配置响应
 * @param result 操作结果
 * @param message 响应消息
 * @param data 响应数据（可以为NULL）
 * @return config_response_t* 响应对象，使用后需要调用config_response_destroy释放
 */
config_response_t* config_response_create(config_result_t result, 
                                         const char *message, 
                                         cJSON *data);

/**
 * @brief 销毁配置响应
 * @param response 响应对象
 */
void config_response_destroy(config_response_t *response);

/**
 * @brief 将配置响应转换为JSON格式
 * @param response 响应对象
 * @return cJSON* JSON对象，使用后需要调用cJSON_Delete释放
 */
cJSON* config_response_to_json(const config_response_t *response);

// ==================== 工具函数 ====================

/**
 * @brief 获取配置结果的字符串描述
 * @param result 配置结果
 * @return const char* 结果描述字符串
 */
const char* config_result_to_string(config_result_t result);

/**
 * @brief 检查配置操作接口的完整性
 * @param ops 配置操作接口
 * @return 1表示接口完整，0表示接口不完整
 */
int config_operations_validate(const config_operations_t *ops);

/**
 * @brief 创建标准错误响应
 * @param result 错误类型
 * @param error_message 错误消息
 * @return config_response_t* 错误响应对象
 */
config_response_t* config_create_error_response(config_result_t result,
                                               const char *error_message);

/**
 * @brief 创建标准成功响应
 * @param message 成功消息
 * @param data 响应数据
 * @return config_response_t* 成功响应对象
 */
config_response_t* config_create_success_response(const char *message,
                                                 cJSON *data);

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_COMMON_CONFIG_BASE_H */ 