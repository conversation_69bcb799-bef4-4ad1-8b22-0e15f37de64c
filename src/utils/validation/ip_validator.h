#ifndef WEBCFG_IP_VALIDATOR_H
#define WEBCFG_IP_VALIDATOR_H

/**
 * IP地址验证工具
 * 提供各种IP地址格式和范围验证功能
 */

/**
 * 验证IP地址格式是否正确
 * @param ip_str IP地址字符串
 * @return 0成功，-1失败
 */
int validate_ip_address(const char *ip_str);

/**
 * 验证IP地址是否在指定范围内
 * @param ip_str 要验证的IP地址
 * @param min_ip 最小IP地址
 * @param max_ip 最大IP地址
 * @return 0在范围内，-1不在范围内或格式错误
 */
int validate_ip_range(const char *ip_str, const char *min_ip, const char *max_ip);

/**
 * 验证IP地址是否在指定子网内
 * @param ip_str 要验证的IP地址
 * @param subnet_ip 子网地址
 * @param mask_str 子网掩码
 * @return 0在子网内，-1不在子网内或格式错误
 */
int validate_ip_in_subnet(const char *ip_str, const char *subnet_ip, const char *mask_str);

/**
 * 验证是否为私有IP地址
 * @param ip_str IP地址字符串
 * @return 0是私有IP，-1不是私有IP或格式错误
 */
int validate_private_ip(const char *ip_str);

/**
 * 验证是否为保留IP地址
 * @param ip_str IP地址字符串
 * @return 0是保留IP，-1不是保留IP或格式错误
 */
int validate_reserved_ip(const char *ip_str);

/**
 * 验证子网掩码格式
 * @param mask_str 子网掩码字符串
 * @return 0有效掩码，-1无效掩码
 */
int validate_subnet_mask(const char *mask_str);

/**
 * 获取子网掩码的前缀长度（CIDR表示法）
 * @param mask_str 子网掩码字符串
 * @return 前缀长度（0-32），-1表示无效掩码
 */
int get_subnet_prefix_length(const char *mask_str);

#endif // WEBCFG_IP_VALIDATOR_H 