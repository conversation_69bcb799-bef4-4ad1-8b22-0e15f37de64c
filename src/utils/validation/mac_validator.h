#ifndef UTILS_VALIDATION_MAC_VALIDATOR_H
#define UTILS_VALIDATION_MAC_VALIDATOR_H

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file mac_validator.h
 * @brief MAC地址验证工具
 * <AUTHOR> Assistant
 * @date 2024-12-26
 */

/**
 * @brief 验证MAC地址格式
 * @param mac_str MAC地址字符串 (格式: xx:xx:xx:xx:xx:xx 或 xx-xx-xx-xx-xx-xx)
 * @return 1表示有效，0表示无效
 */
int validate_mac_address(const char *mac_str);

/**
 * @brief 验证MAC地址格式（严格冒号分隔）
 * @param mac_str MAC地址字符串 (格式: xx:xx:xx:xx:xx:xx)
 * @return 1表示有效，0表示无效
 */
int validate_mac_address_colon(const char *mac_str);

/**
 * @brief 验证MAC地址格式（严格连字符分隔）
 * @param mac_str MAC地址字符串 (格式: xx-xx-xx-xx-xx-xx)
 * @return 1表示有效，0表示无效
 */
int validate_mac_address_dash(const char *mac_str);

/**
 * @brief 检查是否为广播MAC地址
 * @param mac_str MAC地址字符串
 * @return 1表示是广播地址，0表示不是
 */
int is_broadcast_mac(const char *mac_str);

/**
 * @brief 检查是否为单播MAC地址
 * @param mac_str MAC地址字符串
 * @return 1表示是单播地址，0表示不是
 */
int is_unicast_mac(const char *mac_str);

/**
 * @brief 检查是否为组播MAC地址
 * @param mac_str MAC地址字符串
 * @return 1表示是组播地址，0表示不是
 */
int is_multicast_mac(const char *mac_str);

/**
 * @brief 检查是否为本地管理MAC地址
 * @param mac_str MAC地址字符串
 * @return 1表示是本地管理地址，0表示是全球唯一地址
 */
int is_locally_administered_mac(const char *mac_str);

#ifdef __cplusplus
}
#endif

#endif /* UTILS_VALIDATION_MAC_VALIDATOR_H */ 