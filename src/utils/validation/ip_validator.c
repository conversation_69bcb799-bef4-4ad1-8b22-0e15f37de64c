#include "utils/validation/ip_validator.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <arpa/inet.h>
#include <netinet/in.h>

/**
 * @brief 验证IP地址格式
 * @param ip_str IP地址字符串
 * @return 1表示有效，0表示无效
 */
int validate_ip_address(const char *ip_str) {
    if (!ip_str) {
        return 0;
    }
    
    struct sockaddr_in sa;
    int result = inet_pton(AF_INET, ip_str, &(sa.sin_addr));
    return result == 1;
}

/**
 * @brief 验证IP地址范围
 * @param ip_str 要验证的IP地址
 * @param min_ip 最小IP地址
 * @param max_ip 最大IP地址
 * @return 1表示在范围内，0表示不在范围内
 */
int validate_ip_range(const char *ip_str, const char *min_ip, const char *max_ip) {
    if (!ip_str || !min_ip || !max_ip) {
        return 0;
    }
    
    // 验证所有IP地址格式
    if (!validate_ip_address(ip_str) || 
        !validate_ip_address(min_ip) || 
        !validate_ip_address(max_ip)) {
        return 0;
    }
    
    uint32_t ip_num = inet_addr(ip_str);
    uint32_t min_num = inet_addr(min_ip);
    uint32_t max_num = inet_addr(max_ip);
    
    // 转换为主机字节序进行比较
    ip_num = ntohl(ip_num);
    min_num = ntohl(min_num);
    max_num = ntohl(max_num);
    
    return (ip_num >= min_num && ip_num <= max_num);
}

/**
 * @brief 验证IP地址是否在指定子网内
 * @param ip_str IP地址字符串
 * @param subnet_ip 子网地址
 * @param mask_str 子网掩码
 * @return 1表示在子网内，0表示不在
 */
int validate_ip_in_subnet(const char *ip_str, const char *subnet_ip, const char *mask_str) {
    if (!ip_str || !subnet_ip || !mask_str) {
        return 0;
    }
    
    // 验证IP地址和子网掩码格式
    if (!validate_ip_address(ip_str) || 
        !validate_ip_address(subnet_ip) || 
        !validate_subnet_mask(mask_str)) {
        return 0;
    }
    
    uint32_t ip_num = inet_addr(ip_str);
    uint32_t subnet_num = inet_addr(subnet_ip);
    uint32_t mask_num = inet_addr(mask_str);
    
    // 计算网络地址
    uint32_t ip_network = ip_num & mask_num;
    uint32_t subnet_network = subnet_num & mask_num;
    
    return ip_network == subnet_network;
}

/**
 * @brief 验证是否为私有IP地址
 * @param ip_str IP地址字符串
 * @return 1表示是私有IP，0表示不是
 */
int validate_private_ip(const char *ip_str) {
    if (!validate_ip_address(ip_str)) {
        return 0;
    }
    
    uint32_t ip_num = ntohl(inet_addr(ip_str));
    
    // 10.0.0.0/8: 10.0.0.0 - **************
    if ((ip_num >= 0x0A000000) && (ip_num <= 0x0AFFFFFF)) {
        return 1;
    }
    
    // **********/12: ********** - **************
    if ((ip_num >= 0xAC100000) && (ip_num <= 0xAC1FFFFF)) {
        return 1;
    }
    
    // ***********/16: *********** - ***************
    if ((ip_num >= 0xC0A80000) && (ip_num <= 0xC0A8FFFF)) {
        return 1;
    }
    
    return 0;
}

/**
 * @brief 验证是否为保留IP地址
 * @param ip_str IP地址字符串
 * @return 1表示是保留IP，0表示不是
 */
int validate_reserved_ip(const char *ip_str) {
    if (!validate_ip_address(ip_str)) {
        return 0;
    }
    
    uint32_t ip_num = ntohl(inet_addr(ip_str));
    
    // 0.0.0.0/8: 本网络
    if (ip_num <= 0x00FFFFFF) {
        return 1;
    }
    
    // *********/8: 回环地址
    if ((ip_num >= 0x7F000000) && (ip_num <= 0x7FFFFFFF)) {
        return 1;
    }
    
    // ***********/16: 链路本地地址
    if ((ip_num >= 0xA9FE0000) && (ip_num <= 0xA9FEFFFF)) {
        return 1;
    }
    
    // *********/4: 组播地址
    if ((ip_num >= 0xE0000000) && (ip_num <= 0xEFFFFFFF)) {
        return 1;
    }
    
    // 240.0.0.0/4: 保留地址
    if ((ip_num >= 0xF0000000) && (ip_num <= 0xFFFFFFFF)) {
        return 1;
    }
    
    return 0;
}

/**
 * @brief 验证子网掩码
 * @param mask_str 子网掩码字符串
 * @return 1表示有效，0表示无效
 */
int validate_subnet_mask(const char *mask_str) {
    if (!validate_ip_address(mask_str)) {
        return 0;
    }
    
    uint32_t mask_num = ntohl(inet_addr(mask_str));
    
    // 检查掩码是否为连续的1后跟连续的0
    // 例如: ************* = 11111111.11111111.11111111.00000000
    
    // 找到第一个0位
    int found_zero = 0;
    for (int i = 31; i >= 0; i--) {
        if (mask_num & (1U << i)) {
            // 如果已经找到0，但现在又遇到1，说明不连续
            if (found_zero) {
                return 0;
            }
        } else {
            found_zero = 1;
        }
    }
    
    return 1;
}

/**
 * @brief 获取子网前缀长度
 * @param mask_str 子网掩码字符串
 * @return 前缀长度，-1表示无效掩码
 */
int get_subnet_prefix_length(const char *mask_str) {
    if (!validate_subnet_mask(mask_str)) {
        return -1;
    }
    
    uint32_t mask_num = ntohl(inet_addr(mask_str));
    int prefix_len = 0;
    
    // 计算连续的1的个数
    for (int i = 31; i >= 0; i--) {
        if (mask_num & (1U << i)) {
            prefix_len++;
        } else {
            break;
        }
    }
    
    return prefix_len;
} 