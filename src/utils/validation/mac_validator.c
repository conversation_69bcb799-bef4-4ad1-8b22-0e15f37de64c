#include "utils/validation/mac_validator.h"
#include <string.h>
#include <ctype.h>
#include <stdlib.h>
#include <stdio.h>
#include <regex.h>

/**
 * @brief 验证MAC地址格式
 * @param mac_str MAC地址字符串 (格式: xx:xx:xx:xx:xx:xx 或 xx-xx-xx-xx-xx-xx)
 * @return 1表示有效，0表示无效
 */
int validate_mac_address(const char *mac_str) {
    if (!mac_str) {
        return 0;
    }
    
    // 检查长度 (17字符：xx:xx:xx:xx:xx:xx)
    if (strlen(mac_str) != 17) {
        return 0;
    }
    
    // 尝试冒号分隔格式
    if (validate_mac_address_colon(mac_str)) {
        return 1;
    }
    
    // 尝试连字符分隔格式
    if (validate_mac_address_dash(mac_str)) {
        return 1;
    }
    
    return 0;
}

/**
 * @brief 验证MAC地址格式（严格冒号分隔）
 * @param mac_str MAC地址字符串 (格式: xx:xx:xx:xx:xx:xx)
 * @return 1表示有效，0表示无效
 */
int validate_mac_address_colon(const char *mac_str) {
    if (!mac_str || strlen(mac_str) != 17) {
        return 0;
    }
    
    regex_t regex;
    int ret;
    
    // MAC地址正则表达式（冒号分隔）
    const char *pattern = "^([0-9A-Fa-f]{2}:){5}([0-9A-Fa-f]{2})$";
    
    ret = regcomp(&regex, pattern, REG_EXTENDED);
    if (ret) {
        return 0;
    }
    
    ret = regexec(&regex, mac_str, 0, NULL, 0);
    regfree(&regex);
    
    return (ret == 0) ? 1 : 0;
}

/**
 * @brief 验证MAC地址格式（严格连字符分隔）
 * @param mac_str MAC地址字符串 (格式: xx-xx-xx-xx-xx-xx)
 * @return 1表示有效，0表示无效
 */
int validate_mac_address_dash(const char *mac_str) {
    if (!mac_str || strlen(mac_str) != 17) {
        return 0;
    }
    
    regex_t regex;
    int ret;
    
    // MAC地址正则表达式（连字符分隔）
    const char *pattern = "^([0-9A-Fa-f]{2}-){5}([0-9A-Fa-f]{2})$";
    
    ret = regcomp(&regex, pattern, REG_EXTENDED);
    if (ret) {
        return 0;
    }
    
    ret = regexec(&regex, mac_str, 0, NULL, 0);
    regfree(&regex);
    
    return (ret == 0) ? 1 : 0;
}

/**
 * @brief 解析MAC地址为字节数组
 * @param mac_str MAC地址字符串
 * @param mac_bytes 输出的字节数组 (6字节)
 * @return 1表示成功，0表示失败
 */
static int parse_mac_to_bytes(const char *mac_str, unsigned char mac_bytes[6]) {
    if (!mac_str || !mac_bytes) {
        return 0;
    }
    
    if (strlen(mac_str) != 17) {
        return 0;
    }
    
    char delimiter = mac_str[2]; // 获取分隔符
    if (delimiter != ':' && delimiter != '-') {
        return 0;
    }
    
    // 解析MAC地址
    int values[6];
    char format[50];  // 增加缓冲区大小
    snprintf(format, sizeof(format), "%%02x%c%%02x%c%%02x%c%%02x%c%%02x%c%%02x", 
             delimiter, delimiter, delimiter, delimiter, delimiter);
    
    int result = sscanf(mac_str, format,
                       &values[0], &values[1], &values[2],
                       &values[3], &values[4], &values[5]);
    
    if (result != 6) {
        return 0;
    }
    
    for (int i = 0; i < 6; i++) {
        if (values[i] < 0 || values[i] > 255) {
            return 0;
        }
        mac_bytes[i] = (unsigned char)values[i];
    }
    
    return 1;
}

/**
 * @brief 检查是否为广播MAC地址
 * @param mac_str MAC地址字符串
 * @return 1表示是广播地址，0表示不是
 */
int is_broadcast_mac(const char *mac_str) {
    unsigned char mac_bytes[6];
    
    if (!parse_mac_to_bytes(mac_str, mac_bytes)) {
        return 0;
    }
    
    // 广播地址：FF:FF:FF:FF:FF:FF
    for (int i = 0; i < 6; i++) {
        if (mac_bytes[i] != 0xFF) {
            return 0;
        }
    }
    
    return 1;
}

/**
 * @brief 检查是否为单播MAC地址
 * @param mac_str MAC地址字符串
 * @return 1表示是单播地址，0表示不是
 */
int is_unicast_mac(const char *mac_str) {
    unsigned char mac_bytes[6];
    
    if (!parse_mac_to_bytes(mac_str, mac_bytes)) {
        return 0;
    }
    
    // 单播地址：第一个字节的最低位为0
    return (mac_bytes[0] & 0x01) == 0;
}

/**
 * @brief 检查是否为组播MAC地址
 * @param mac_str MAC地址字符串
 * @return 1表示是组播地址，0表示不是
 */
int is_multicast_mac(const char *mac_str) {
    unsigned char mac_bytes[6];
    
    if (!parse_mac_to_bytes(mac_str, mac_bytes)) {
        return 0;
    }
    
    // 组播地址：第一个字节的最低位为1
    return (mac_bytes[0] & 0x01) == 1;
}

/**
 * @brief 检查是否为本地管理MAC地址
 * @param mac_str MAC地址字符串
 * @return 1表示是本地管理地址，0表示是全球唯一地址
 */
int is_locally_administered_mac(const char *mac_str) {
    unsigned char mac_bytes[6];
    
    if (!parse_mac_to_bytes(mac_str, mac_bytes)) {
        return 0;
    }
    
    // 本地管理地址：第一个字节的第二位为1
    return (mac_bytes[0] & 0x02) == 2;
} 