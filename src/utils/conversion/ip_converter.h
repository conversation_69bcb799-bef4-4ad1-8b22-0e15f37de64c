#ifndef WEBCFG_IP_CONVERTER_H
#define WEBCFG_IP_CONVERTER_H

#include <stdint.h>
#include <stddef.h>

/**
 * IP地址转换工具
 * 提供IP地址在不同格式间的转换功能
 */

/**
 * IP地址字符串转换为32位整数（网络字节序）
 * @param ip_str IP地址字符串
 * @return 32位IP地址，INADDR_NONE表示失败
 */
uint32_t ip_str_to_uint32(const char *ip_str);

/**
 * 32位整数（网络字节序）转换为IP地址字符串
 * @param ip_num 32位IP地址
 * @param ip_str 输出缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_uint32_to_str(uint32_t ip_num, char *ip_str, size_t size);

/**
 * IP地址字符串转换为32位整数（主机字节序）
 * @param ip_str IP地址字符串
 * @return 32位IP地址（主机字节序），0表示失败
 */
uint32_t ip_str_to_uint32_host(const char *ip_str);

/**
 * 32位整数（主机字节序）转换为IP地址字符串
 * @param ip_num 32位IP地址（主机字节序）
 * @param ip_str 输出缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_uint32_host_to_str(uint32_t ip_num, char *ip_str, size_t size);

/**
 * 检查IP地址是否在指定子网内
 * @param ip_str 要检查的IP地址
 * @param subnet_str 子网地址
 * @param mask_str 子网掩码
 * @return 0在子网内，-1不在子网内或参数错误
 */
int ip_is_in_subnet(const char *ip_str, const char *subnet_str, const char *mask_str);

/**
 * 计算网络地址
 * @param ip_str IP地址字符串
 * @param mask_str 子网掩码字符串
 * @param network_str 输出网络地址缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_get_network_address(const char *ip_str, const char *mask_str, 
                          char *network_str, size_t size);

/**
 * 计算广播地址
 * @param ip_str IP地址字符串
 * @param mask_str 子网掩码字符串
 * @param broadcast_str 输出广播地址缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_get_broadcast_address(const char *ip_str, const char *mask_str, 
                           char *broadcast_str, size_t size);

/**
 * CIDR前缀长度转换为子网掩码
 * @param prefix_len 前缀长度（0-32）
 * @param mask_str 输出子网掩码缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_prefix_to_mask(int prefix_len, char *mask_str, size_t size);

/**
 * 计算子网中的主机数量
 * @param mask_str 子网掩码字符串
 * @return 主机数量，-1表示失败
 */
int ip_get_host_count(const char *mask_str);

#endif // WEBCFG_IP_CONVERTER_H 