#include "utils/conversion/mac_converter.h"
#include "utils/validation/mac_validator.h"
#include <stdio.h>
#include <string.h>
#include <ctype.h>
#include <stdlib.h>
#include <time.h>

/**
 * @brief MAC地址字符串转换为字节数组
 * @param mac_str MAC地址字符串 (支持: xx:xx:xx:xx:xx:xx 或 xx-xx-xx-xx-xx-xx)
 * @param mac_bytes 输出的字节数组 (6字节)
 * @return 0表示成功，-1表示失败
 */
int mac_str_to_bytes(const char *mac_str, uint8_t mac_bytes[6]) {
    if (!mac_str || !mac_bytes) {
        return -1;
    }
    
    // 验证MAC地址格式
    if (!validate_mac_address(mac_str)) {
        return -1;
    }
    
    char delimiter = mac_str[2]; // 获取分隔符
    if (delimiter != ':' && delimiter != '-') {
        return -1;
    }
    
    // 解析MAC地址
    unsigned int values[6];
    char format[50];
    snprintf(format, sizeof(format), "%%02x%c%%02x%c%%02x%c%%02x%c%%02x%c%%02x", 
             delimiter, delimiter, delimiter, delimiter, delimiter);
    
    int result = sscanf(mac_str, format,
                       &values[0], &values[1], &values[2],
                       &values[3], &values[4], &values[5]);
    
    if (result != 6) {
        return -1;
    }
    
    for (int i = 0; i < 6; i++) {
        if (values[i] > 255) {
            return -1;
        }
        mac_bytes[i] = (uint8_t)values[i];
    }
    
    return 0;
}

/**
 * @brief 字节数组转换为MAC地址字符串（冒号分隔）
 * @param mac_bytes 输入的字节数组 (6字节)
 * @param mac_str 输出的MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_bytes_to_str(const uint8_t mac_bytes[6], char *mac_str, size_t size) {
    if (!mac_bytes || !mac_str || size < 18) {
        return -1;
    }
    
    int ret = snprintf(mac_str, size, "%02x:%02x:%02x:%02x:%02x:%02x",
                      mac_bytes[0], mac_bytes[1], mac_bytes[2],
                      mac_bytes[3], mac_bytes[4], mac_bytes[5]);
    
    if (ret < 0 || (size_t)ret >= size) {
        return -1;
    }
    
    return 0;
}

/**
 * @brief 字节数组转换为MAC地址字符串（连字符分隔）
 * @param mac_bytes 输入的字节数组 (6字节)
 * @param mac_str 输出的MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_bytes_to_str_dash(const uint8_t mac_bytes[6], char *mac_str, size_t size) {
    if (!mac_bytes || !mac_str || size < 18) {
        return -1;
    }
    
    int ret = snprintf(mac_str, size, "%02x-%02x-%02x-%02x-%02x-%02x",
                      mac_bytes[0], mac_bytes[1], mac_bytes[2],
                      mac_bytes[3], mac_bytes[4], mac_bytes[5]);
    
    if (ret < 0 || (size_t)ret >= size) {
        return -1;
    }
    
    return 0;
}

/**
 * @brief 标准化MAC地址格式（转换为冒号分隔的大写格式）
 * @param input_mac 输入的MAC地址字符串
 * @param output_mac 输出的标准化MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_normalize(const char *input_mac, char *output_mac, size_t size) {
    if (!input_mac || !output_mac || size < 18) {
        return -1;
    }
    
    uint8_t mac_bytes[6];
    if (mac_str_to_bytes(input_mac, mac_bytes) != 0) {
        return -1;
    }
    
    int ret = snprintf(output_mac, size, "%02X:%02X:%02X:%02X:%02X:%02X",
                      mac_bytes[0], mac_bytes[1], mac_bytes[2],
                      mac_bytes[3], mac_bytes[4], mac_bytes[5]);
    
    if (ret < 0 || (size_t)ret >= size) {
        return -1;
    }
    
    return 0;
}

/**
 * @brief 标准化MAC地址格式（转换为冒号分隔的小写格式）
 * @param input_mac 输入的MAC地址字符串
 * @param output_mac 输出的标准化MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_normalize_lower(const char *input_mac, char *output_mac, size_t size) {
    if (!input_mac || !output_mac || size < 18) {
        return -1;
    }
    
    uint8_t mac_bytes[6];
    if (mac_str_to_bytes(input_mac, mac_bytes) != 0) {
        return -1;
    }
    
    return mac_bytes_to_str(mac_bytes, output_mac, size);
}

/**
 * @brief 比较两个MAC地址是否相等
 * @param mac1 MAC地址字符串1
 * @param mac2 MAC地址字符串2
 * @return 1表示相等，0表示不相等，-1表示输入无效
 */
int mac_compare(const char *mac1, const char *mac2) {
    if (!mac1 || !mac2) {
        return -1;
    }
    
    uint8_t bytes1[6], bytes2[6];
    
    if (mac_str_to_bytes(mac1, bytes1) != 0 || 
        mac_str_to_bytes(mac2, bytes2) != 0) {
        return -1;
    }
    
    return (memcmp(bytes1, bytes2, 6) == 0) ? 1 : 0;
}

/**
 * @brief 生成随机MAC地址（本地管理地址）
 * @param mac_str 输出的MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_generate_random(char *mac_str, size_t size) {
    if (!mac_str || size < 18) {
        return -1;
    }
    
    static int seeded = 0;
    if (!seeded) {
        srand((unsigned int)time(NULL));
        seeded = 1;
    }
    
    uint8_t mac_bytes[6];
    
    // 生成随机MAC地址
    for (int i = 0; i < 6; i++) {
        mac_bytes[i] = (uint8_t)(rand() & 0xFF);
    }
    
    // 设置为本地管理地址：第一个字节的第二位设为1
    mac_bytes[0] |= 0x02;
    // 确保是单播地址：第一个字节的最低位设为0
    mac_bytes[0] &= 0xFE;
    
    return mac_bytes_to_str(mac_bytes, mac_str, size);
}

/**
 * @brief 基于OUI生成MAC地址
 * @param oui 组织唯一标识符 (前3字节)
 * @param mac_str 输出的MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_generate_with_oui(const uint8_t oui[3], char *mac_str, size_t size) {
    if (!oui || !mac_str || size < 18) {
        return -1;
    }
    
    static int seeded = 0;
    if (!seeded) {
        srand((unsigned int)time(NULL));
        seeded = 1;
    }
    
    uint8_t mac_bytes[6];
    
    // 设置OUI（前3字节）
    mac_bytes[0] = oui[0];
    mac_bytes[1] = oui[1];
    mac_bytes[2] = oui[2];
    
    // 生成随机的后3字节
    for (int i = 3; i < 6; i++) {
        mac_bytes[i] = (uint8_t)(rand() & 0xFF);
    }
    
    return mac_bytes_to_str(mac_bytes, mac_str, size);
} 