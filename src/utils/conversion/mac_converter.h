#ifndef UTILS_CONVERSION_MAC_CONVERTER_H
#define UTILS_CONVERSION_MAC_CONVERTER_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @file mac_converter.h
 * @brief MAC地址转换工具
 * <AUTHOR> Assistant
 * @date 2024-12-26
 */

/**
 * @brief MAC地址字符串转换为字节数组
 * @param mac_str MAC地址字符串 (支持: xx:xx:xx:xx:xx:xx 或 xx-xx-xx-xx-xx-xx)
 * @param mac_bytes 输出的字节数组 (6字节)
 * @return 0表示成功，-1表示失败
 */
int mac_str_to_bytes(const char *mac_str, uint8_t mac_bytes[6]);

/**
 * @brief 字节数组转换为MAC地址字符串（冒号分隔）
 * @param mac_bytes 输入的字节数组 (6字节)
 * @param mac_str 输出的MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_bytes_to_str(const uint8_t mac_bytes[6], char *mac_str, size_t size);

/**
 * @brief 字节数组转换为MAC地址字符串（连字符分隔）
 * @param mac_bytes 输入的字节数组 (6字节)
 * @param mac_str 输出的MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_bytes_to_str_dash(const uint8_t mac_bytes[6], char *mac_str, size_t size);

/**
 * @brief 标准化MAC地址格式（转换为冒号分隔的大写格式）
 * @param input_mac 输入的MAC地址字符串
 * @param output_mac 输出的标准化MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_normalize(const char *input_mac, char *output_mac, size_t size);

/**
 * @brief 标准化MAC地址格式（转换为冒号分隔的小写格式）
 * @param input_mac 输入的MAC地址字符串
 * @param output_mac 输出的标准化MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_normalize_lower(const char *input_mac, char *output_mac, size_t size);

/**
 * @brief 比较两个MAC地址是否相等
 * @param mac1 MAC地址字符串1
 * @param mac2 MAC地址字符串2
 * @return 1表示相等，0表示不相等，-1表示输入无效
 */
int mac_compare(const char *mac1, const char *mac2);

/**
 * @brief 生成随机MAC地址（本地管理地址）
 * @param mac_str 输出的MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_generate_random(char *mac_str, size_t size);

/**
 * @brief 基于OUI生成MAC地址
 * @param oui 组织唯一标识符 (前3字节)
 * @param mac_str 输出的MAC地址字符串缓冲区 (至少18字节)
 * @param size 缓冲区大小
 * @return 0表示成功，-1表示失败
 */
int mac_generate_with_oui(const uint8_t oui[3], char *mac_str, size_t size);

#ifdef __cplusplus
}
#endif

#endif /* UTILS_CONVERSION_MAC_CONVERTER_H */ 