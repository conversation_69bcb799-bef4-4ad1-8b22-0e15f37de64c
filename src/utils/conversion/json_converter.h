#ifndef WEBCFG_JSON_CONVERTER_H
#define WEBCFG_JSON_CONVERTER_H

#include <cJSON.h>
#include <stdint.h>
#include <stddef.h>

/**
 * JSON转换工具
 * 提供通用的JSON数据验证、提取和转换功能
 */

/**
 * 验证JSON对象是否包含必需字段
 * @param json JSON对象
 * @param fields 必需字段名数组，以NULL结尾
 * @return 0成功，-1缺少必需字段
 */
int json_validate_required_fields(const cJSON *json, const char *fields[]);

/**
 * 验证JSON数字字段的范围
 * @param json JSON对象
 * @param field 字段名
 * @param min 最小值
 * @param max 最大值
 * @return 0在范围内，-1不在范围内或字段不存在
 */
int json_validate_number_range(const cJSON *json, const char *field, double min, double max);

/**
 * 验证JSON字符串字段的长度
 * @param json JSON对象
 * @param field 字段名
 * @param max_len 最大长度（不包括\0）
 * @return 0长度有效，-1长度超限或字段不存在
 */
int json_validate_string_length(const cJSON *json, const char *field, size_t max_len);

/**
 * 验证JSON字符串字段是否为有效IP地址
 * @param json JSON对象
 * @param field 字段名
 * @return 0有效IP，-1无效IP或字段不存在
 */
int json_validate_ip_field(const cJSON *json, const char *field);

/**
 * 从JSON对象中提取uint32_t值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_uint32(const cJSON *json, const char *field, uint32_t *value);

/**
 * 从JSON对象中提取uint16_t值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_uint16(const cJSON *json, const char *field, uint16_t *value);

/**
 * 从JSON对象中提取uint8_t值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_uint8(const cJSON *json, const char *field, uint8_t *value);

/**
 * 从JSON对象中提取字符串值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int json_get_string(const cJSON *json, const char *field, char *value, size_t size);

/**
 * 从JSON对象中提取布尔值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_bool(const cJSON *json, const char *field, uint8_t *value);

/**
 * 从JSON对象中提取IP地址字符串并转换为uint32_t
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出IP地址（网络字节序）
 * @return 0成功，-1失败
 */
int json_get_ip_as_uint32(const cJSON *json, const char *field, uint32_t *value);

/**
 * 向JSON对象添加IP地址字段（从uint32_t转换）
 * @param json JSON对象
 * @param field 字段名
 * @param ip_value IP地址（网络字节序）
 * @return 0成功，-1失败
 */
int json_add_ip_from_uint32(cJSON *json, const char *field, uint32_t ip_value);

#endif // WEBCFG_JSON_CONVERTER_H 