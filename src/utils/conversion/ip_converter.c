#include "ip_converter.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>

/**
 * IP地址字符串转换为32位整数（网络字节序）
 * @param ip_str IP地址字符串
 * @return 32位IP地址，INADDR_NONE表示失败
 */
uint32_t ip_str_to_uint32(const char *ip_str) {
    if (!ip_str) {
        return INADDR_NONE;
    }
    
    return inet_addr(ip_str);
}

/**
 * 32位整数（网络字节序）转换为IP地址字符串
 * @param ip_num 32位IP地址
 * @param ip_str 输出缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_uint32_to_str(uint32_t ip_num, char *ip_str, size_t size) {
    if (!ip_str || size < INET_ADDRSTRLEN) {
        return -1;
    }
    
    struct in_addr addr;
    addr.s_addr = ip_num;
    
    const char *result = inet_ntoa(addr);
    if (!result) {
        return -1;
    }
    
    strncpy(ip_str, result, size - 1);
    ip_str[size - 1] = '\0';
    
    return 0;
}

/**
 * IP地址字符串转换为32位整数（主机字节序）
 * @param ip_str IP地址字符串
 * @return 32位IP地址（主机字节序），0表示失败
 */
uint32_t ip_str_to_uint32_host(const char *ip_str) {
    uint32_t ip_net = ip_str_to_uint32(ip_str);
    if (ip_net == INADDR_NONE) {
        return 0;
    }
    
    return ntohl(ip_net);
}

/**
 * 32位整数（主机字节序）转换为IP地址字符串
 * @param ip_num 32位IP地址（主机字节序）
 * @param ip_str 输出缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_uint32_host_to_str(uint32_t ip_num, char *ip_str, size_t size) {
    uint32_t ip_net = htonl(ip_num);
    return ip_uint32_to_str(ip_net, ip_str, size);
}

/**
 * 检查IP地址是否在指定子网内
 * @param ip_str 要检查的IP地址
 * @param subnet_str 子网地址
 * @param mask_str 子网掩码
 * @return 0在子网内，-1不在子网内或参数错误
 */
int ip_is_in_subnet(const char *ip_str, const char *subnet_str, const char *mask_str) {
    if (!ip_str || !subnet_str || !mask_str) {
        return -1;
    }
    
    uint32_t ip = ip_str_to_uint32(ip_str);
    uint32_t subnet = ip_str_to_uint32(subnet_str);
    uint32_t mask = ip_str_to_uint32(mask_str);
    
    if (ip == INADDR_NONE || subnet == INADDR_NONE || mask == INADDR_NONE) {
        return -1;
    }
    
    return ((ip & mask) == (subnet & mask)) ? 0 : -1;
}

/**
 * 计算网络地址
 * @param ip_str IP地址字符串
 * @param mask_str 子网掩码字符串
 * @param network_str 输出网络地址缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_get_network_address(const char *ip_str, const char *mask_str, 
                          char *network_str, size_t size) {
    if (!ip_str || !mask_str || !network_str || size < INET_ADDRSTRLEN) {
        return -1;
    }
    
    uint32_t ip = ip_str_to_uint32(ip_str);
    uint32_t mask = ip_str_to_uint32(mask_str);
    
    if (ip == INADDR_NONE || mask == INADDR_NONE) {
        return -1;
    }
    
    uint32_t network = ip & mask;
    return ip_uint32_to_str(network, network_str, size);
}

/**
 * 计算广播地址
 * @param ip_str IP地址字符串
 * @param mask_str 子网掩码字符串
 * @param broadcast_str 输出广播地址缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_get_broadcast_address(const char *ip_str, const char *mask_str, 
                           char *broadcast_str, size_t size) {
    if (!ip_str || !mask_str || !broadcast_str || size < INET_ADDRSTRLEN) {
        return -1;
    }
    
    uint32_t ip = ip_str_to_uint32(ip_str);
    uint32_t mask = ip_str_to_uint32(mask_str);
    
    if (ip == INADDR_NONE || mask == INADDR_NONE) {
        return -1;
    }
    
    uint32_t network = ip & mask;
    uint32_t broadcast = network | (~mask);
    
    return ip_uint32_to_str(broadcast, broadcast_str, size);
}

/**
 * CIDR前缀长度转换为子网掩码
 * @param prefix_len 前缀长度（0-32）
 * @param mask_str 输出子网掩码缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int ip_prefix_to_mask(int prefix_len, char *mask_str, size_t size) {
    if (prefix_len < 0 || prefix_len > 32 || !mask_str || size < INET_ADDRSTRLEN) {
        return -1;
    }
    
    uint32_t mask;
    if (prefix_len == 0) {
        mask = 0;
    } else {
        mask = htonl(0xFFFFFFFF << (32 - prefix_len));
    }
    
    return ip_uint32_to_str(mask, mask_str, size);
}

/**
 * 计算子网中的主机数量
 * @param mask_str 子网掩码字符串
 * @return 主机数量，-1表示失败
 */
int ip_get_host_count(const char *mask_str) {
    if (!mask_str) {
        return -1;
    }
    
    uint32_t mask = ip_str_to_uint32(mask_str);
    if (mask == INADDR_NONE) {
        return -1;
    }
    
    uint32_t host_bits = ~mask;
    
    // 计算主机位数
    int count = 0;
    while (host_bits) {
        if (host_bits & 1) {
            count++;
        }
        host_bits >>= 1;
    }
    
    // 主机数量 = 2^主机位数 - 2（减去网络地址和广播地址）
    if (count == 0) {
        return 0; // 主机位为0，只有一个地址
    } else if (count >= 31) {
        return -1; // 避免整数溢出
    }
    
    int host_count = (1 << count) - 2;
    return host_count > 0 ? host_count : 0;
} 