#include "utils/conversion/json_converter.h"
#include "utils/validation/ip_validator.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdint.h>
#include <arpa/inet.h>
#include <netinet/in.h>

/**
 * @brief 验证JSON对象是否包含必需字段
 * @param json JSON对象
 * @param fields 必需字段名数组，以NULL结尾
 * @return 0成功，-1缺少必需字段
 */
int json_validate_required_fields(const cJSON *json, const char *fields[]) {
    if (!json || !fields) {
        return -1;
    }
    
    for (int i = 0; fields[i] != NULL; i++) {
        if (!cJSON_GetObjectItem(json, fields[i])) {
            fprintf(stderr, "Missing required field: %s\n", fields[i]);
            return -1;
        }
    }
    
    return 0;
}

/**
 * @brief 验证JSON数字字段的范围
 * @param json JSON对象
 * @param field 字段名
 * @param min 最小值
 * @param max 最大值
 * @return 0在范围内，-1不在范围内或字段不存在
 */
int json_validate_number_range(const cJSON *json, const char *field, double min, double max) {
    if (!json || !field) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsNumber(item)) {
        return -1;
    }
    
    double value = cJSON_GetNumberValue(item);
    if (value < min || value > max) {
        fprintf(stderr, "Field %s value %.2f out of range [%.2f, %.2f]\n", 
                field, value, min, max);
        return -1;
    }
    
    return 0;
}

/**
 * @brief 验证JSON字符串字段的长度
 * @param json JSON对象
 * @param field 字段名
 * @param max_len 最大长度（不包括\0）
 * @return 0长度有效，-1长度超限或字段不存在
 */
int json_validate_string_length(const cJSON *json, const char *field, size_t max_len) {
    if (!json || !field) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsString(item)) {
        fprintf(stderr, "Field '%s' is not a string or does not exist\n", field);
        return -1;
    }
    
    size_t len = strlen(item->valuestring);
    if (len > max_len) {
        fprintf(stderr, "Field '%s' length %zu exceeds maximum %zu\n", 
               field, len, max_len);
        return -1;
    }
    
    return 0;
}

/**
 * @brief 验证JSON字符串字段是否为有效IP地址
 * @param json JSON对象
 * @param field 字段名
 * @return 0有效IP，-1无效IP或字段不存在
 */
int json_validate_ip_field(const cJSON *json, const char *field) {
    if (!json || !field) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsString(item)) {
        fprintf(stderr, "Field '%s' is not a string or does not exist\n", field);
        return -1;
    }
    
    if (!validate_ip_address(item->valuestring)) {
        fprintf(stderr, "Invalid IP address in field %s: %s\n", 
                field, item->valuestring);
        return -1;
    }
    
    return 0;
}

/**
 * @brief 从JSON对象中提取uint32_t值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_uint32(const cJSON *json, const char *field, uint32_t *value) {
    if (!json || !field || !value) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsNumber(item)) {
        return -1;
    }
    
    // 检查数值范围
    double dval = cJSON_GetNumberValue(item);
    if (dval < 0 || dval > UINT32_MAX) {
        return -1;
    }
    
    *value = (uint32_t)dval;
    return 0;
}

/**
 * @brief 从JSON对象中提取uint16_t值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_uint16(const cJSON *json, const char *field, uint16_t *value) {
    if (!json || !field || !value) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsNumber(item)) {
        return -1;
    }
    
    // 检查数值范围
    double dval = cJSON_GetNumberValue(item);
    if (dval < 0 || dval > UINT16_MAX) {
        return -1;
    }
    
    *value = (uint16_t)dval;
    return 0;
}

/**
 * @brief 从JSON对象中提取uint8_t值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_uint8(const cJSON *json, const char *field, uint8_t *value) {
    if (!json || !field || !value) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsNumber(item)) {
        return -1;
    }
    
    // 检查数值范围
    double dval = cJSON_GetNumberValue(item);
    if (dval < 0 || dval > UINT8_MAX) {
        return -1;
    }
    
    *value = (uint8_t)dval;
    return 0;
}

/**
 * @brief 获取JSON字符串字段值
 * @param json JSON对象
 * @param field 字段名
 * @param buffer 输出缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int json_get_string_field(const cJSON *json, const char *field, char *buffer, size_t size) {
    if (!json || !field || !buffer || size == 0) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsString(item)) {
        return -1;
    }
    
    strncpy(buffer, item->valuestring, size - 1);
    buffer[size - 1] = '\0';
    
    return 0;
}

/**
 * @brief 获取JSON数字字段值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_number_field(const cJSON *json, const char *field, double *value) {
    if (!json || !field || !value) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsNumber(item)) {
        return -1;
    }
    
    *value = cJSON_GetNumberValue(item);
    return 0;
}

/**
 * @brief 获取JSON整数字段值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_int_field(const cJSON *json, const char *field, int *value) {
    if (!json || !field || !value) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item || !cJSON_IsNumber(item)) {
        return -1;
    }
    
    *value = item->valueint;
    return 0;
}

/**
 * @brief 获取JSON布尔字段值
 * @param json JSON对象
 * @param field 字段名
 * @param value 输出值指针
 * @return 0成功，-1失败
 */
int json_get_bool_field(const cJSON *json, const char *field, int *value) {
    if (!json || !field || !value) {
        return -1;
    }
    
    cJSON *item = cJSON_GetObjectItem(json, field);
    if (!item) {
        return -1;
    }
    
    if (cJSON_IsBool(item)) {
        *value = cJSON_IsTrue(item) ? 1 : 0;
        return 0;
    }
    
    return -1;
}

/**
 * @brief IP地址字符串转网络字节序
 * @param ip_str IP地址字符串
 * @param ip_addr 输出网络字节序IP
 * @return 0成功，-1失败
 */
int json_ip_str_to_addr(const char *ip_str, uint32_t *ip_addr) {
    if (!ip_str || !ip_addr) {
        return -1;
    }
    
    struct in_addr addr;
    if (inet_aton(ip_str, &addr) == 0) {
        return -1;
    }
    
    *ip_addr = addr.s_addr;
    return 0;
}

/**
 * @brief 网络字节序IP转字符串
 * @param ip_addr 网络字节序IP
 * @param ip_str 输出字符串缓冲区
 * @param size 缓冲区大小
 * @return 0成功，-1失败
 */
int json_ip_addr_to_str(uint32_t ip_addr, char *ip_str, size_t size) {
    if (!ip_str || size < INET_ADDRSTRLEN) {
        return -1;
    }
    
    struct in_addr addr;
    addr.s_addr = ip_addr;
    
    const char *result = inet_ntoa(addr);
    if (!result) {
        return -1;
    }
    
    strncpy(ip_str, result, size - 1);
    ip_str[size - 1] = '\0';
    
    return 0;
}

/**
 * @brief 向JSON对象添加IP地址字段（从uint32_t转换）
 * @param json JSON对象
 * @param field 字段名
 * @param ip_value IP地址（网络字节序）
 * @return 0成功，-1失败
 */
int json_add_ip_from_uint32(cJSON *json, const char *field, uint32_t ip_value) {
    if (!json || !field) {
        return -1;
    }
    
    struct in_addr addr;
    addr.s_addr = ip_value;
    char *ip_str = inet_ntoa(addr);
    
    if (!ip_str) {
        return -1;
    }
    
    cJSON *ip_item = cJSON_CreateString(ip_str);
    if (!ip_item) {
        return -1;
    }
    
    cJSON_AddItemToObject(json, field, ip_item);
    return 0;
} 