# utils模块CMakeLists.txt

set(UTILS_SOURCES
    file_utils.c
    string_utils.c
    network_utils.c
    log_utils.c
)

set(UTILS_HEADERS
    ${CMAKE_SOURCE_DIR}/include/utils/file_utils.h
    ${CMAKE_SOURCE_DIR}/include/utils/string_utils.h
    ${CMAKE_SOURCE_DIR}/include/utils/network_utils.h
    ${CMAKE_SOURCE_DIR}/include/utils/log_utils.h
)

# 创建静态库
add_library(webcfg-utils STATIC ${UTILS_SOURCES})

# 包含目录
target_include_directories(webcfg-utils
    PUBLIC ${CMAKE_SOURCE_DIR}/include
    PRIVATE ${CMAKE_SOURCE_DIR}/include/utils
)

# 链接依赖
target_link_libraries(webcfg-utils
    cjson
) 