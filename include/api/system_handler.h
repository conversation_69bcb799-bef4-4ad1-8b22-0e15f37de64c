#ifndef WEBCFG_SYSTEM_HANDLER_H
#define WEBCFG_SYSTEM_HANDLER_H

#include <microhttpd.h>
#include <cJSON.h>

/**
 * 系统信息获取处理
 */
int system_handle_info(struct MHD_Connection *connection,
                      const char *url,
                      cJSON *request_data);

/**
 * 系统重启处理
 */
int system_handle_reboot(struct MHD_Connection *connection,
                        const char *url,
                        cJSON *request_data);

/**
 * 系统日志获取处理
 */
int system_handle_logs(struct MHD_Connection *connection,
                      const char *url,
                      cJSON *request_data);

#endif // WEBCFG_SYSTEM_HANDLER_H 