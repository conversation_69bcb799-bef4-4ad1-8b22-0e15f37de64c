#ifndef WEBCFG_AUTH_HANDLER_H
#define WEBCFG_AUTH_HANDLER_H

#include <microhttpd.h>
#include <cJSON.h>
#include <time.h>

// 认证令牌结构
typedef struct {
    char token[64];             // 认证令牌
    char username[32];          // 用户名
    time_t expires;             // 过期时间
    int privileges;             // 权限级别
} auth_token_t;

// 用户权限级别
#define PRIVILEGE_GUEST    0    // 游客权限
#define PRIVILEGE_USER     1    // 普通用户权限
#define PRIVILEGE_ADMIN    2    // 管理员权限

/**
 * 初始化认证系统
 * @return 0成功，-1失败
 */
int auth_init(void);

/**
 * 销毁认证系统
 */
void auth_destroy(void);

/**
 * 用户登录处理
 * @param connection MHD连接
 * @param url 请求URL
 * @param request_data 请求数据
 * @return MHD处理结果
 */
int auth_handle_login(struct MHD_Connection *connection,
                     const char *url,
                     cJSON *request_data);

/**
 * 用户登出处理
 * @param connection MHD连接
 * @param url 请求URL
 * @param request_data 请求数据
 * @return MHD处理结果
 */
int auth_handle_logout(struct MHD_Connection *connection,
                      const char *url,
                      cJSON *request_data);

/**
 * 认证状态检查
 * @param connection MHD连接
 * @param url 请求URL
 * @param request_data 请求数据
 * @return MHD处理结果
 */
int auth_handle_status(struct MHD_Connection *connection,
                      const char *url,
                      cJSON *request_data);

/**
 * 验证用户凭据
 * @param username 用户名
 * @param password 密码
 * @return 权限级别，-1表示验证失败
 */
int auth_verify_credentials(const char *username, const char *password);

/**
 * 创建认证令牌
 * @param username 用户名
 * @param privileges 权限级别
 * @return 令牌指针，失败返回NULL
 */
auth_token_t* auth_create_token(const char *username, int privileges);

/**
 * 验证认证令牌
 * @param token_str 令牌字符串
 * @return 令牌指针，失败返回NULL
 */
auth_token_t* auth_verify_token(const char *token_str);

/**
 * 检查请求认证
 * @param connection MHD连接
 * @param required_privilege 所需权限级别
 * @return 认证令牌指针，失败返回NULL
 */
auth_token_t* auth_check_request(struct MHD_Connection *connection,
                                int required_privilege);

/**
 * 销毁认证令牌
 * @param token 令牌指针
 */
void auth_destroy_token(auth_token_t *token);

/**
 * 生成随机令牌字符串
 * @param buffer 输出缓冲区
 * @param length 缓冲区长度
 */
void auth_generate_token_string(char *buffer, size_t length);

#endif // WEBCFG_AUTH_HANDLER_H 