#ifndef WEBCFG_CONFIG_HANDLER_H
#define WEBCFG_CONFIG_HANDLER_H

#include <microhttpd.h>
#include <cJSON.h>

/**
 * 网络配置获取处理
 */
int config_handle_network_get(struct MHD_Connection *connection,
                             const char *url,
                             cJSON *request_data);

/**
 * 网络配置保存处理
 */
int config_handle_network_post(struct MHD_Connection *connection,
                              const char *url,
                              cJSON *request_data);

/**
 * 设备配置获取处理
 */
int config_handle_device_get(struct MHD_Connection *connection,
                            const char *url,
                            cJSON *request_data);

/**
 * 设备配置保存处理
 */
int config_handle_device_post(struct MHD_Connection *connection,
                             const char *url,
                             cJSON *request_data);

#endif // WEBCFG_CONFIG_HANDLER_H 