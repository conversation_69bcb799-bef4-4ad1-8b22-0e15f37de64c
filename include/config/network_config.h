#ifndef WEBCFG_NETWORK_CONFIG_H
#define WEBCFG_NETWORK_CONFIG_H

#include <cJSON.h>
#include <stdint.h>
#include <arpa/inet.h>

// 网络配置模块头文件
// TODO: 第二阶段将完整实现

// 网络配置JSON结构（用于API交互）
typedef struct {
    char ip[16];          // IP地址字符串
    char mask[16];        // 子网掩码
    char gateway[16];     // 网关地址  
    char dns[16];         // DNS服务器
    char mac[18];         // MAC地址
} network_config_json_t;

// 二进制网络配置结构（与原有配置文件兼容）
typedef struct {
    uint32_t ip;          // IP地址（网络字节序）
    uint32_t mask;        // 子网掩码
    uint32_t gateway;     // 网关
    uint32_t dns;         // DNS服务器
    uint8_t mac[6];       // MAC地址（6字节）
} __attribute__((packed)) network_config_binary_t;

// 配置文件路径常量
#define NETWORK_CONFIG_FILE "/etc/eth0-setting"
#define NETWORK_SETTING_FILE "/etc/network-setting"

/**
 * 验证IP地址格式
 * @param ip IP地址字符串
 * @return 1有效，0无效
 */
// IP和MAC地址验证函数已移至utils模块

/**
 * 验证网络配置数据
 * @param config 网络配置JSON结构
 * @return 0成功，-1失败
 */
int validate_network_config(const network_config_json_t *config);

/**
 * JSON转换为二进制配置
 * @param json_data JSON数据
 * @param binary_config 输出二进制配置
 * @return 0成功，-1失败
 */
int convert_network_json_to_binary(const cJSON *json_data, network_config_binary_t *binary_config);

/**
 * 二进制配置转换为JSON
 * @param binary_config 二进制配置
 * @param json_data 输出JSON数据
 * @return 0成功，-1失败
 */
int convert_network_binary_to_json(const network_config_binary_t *binary_config, cJSON **json_data);

/**
 * 读取网络配置文件
 * @param config 输出配置数据
 * @return 0成功，-1失败
 */
int read_network_config_file(network_config_binary_t *config);

/**
 * 写入网络配置文件
 * @param config 配置数据
 * @return 0成功，-1失败
 */
int write_network_config_file(const network_config_binary_t *config);

/**
 * MAC地址字符串转换为字节数组
 * @param mac_str MAC地址字符串（格式：xx:xx:xx:xx:xx:xx）
 * @param mac_bytes 输出字节数组（6字节）
 * @return 0成功，-1失败
 */
// MAC地址转换函数已移至utils/conversion/mac_converter.h

#endif // WEBCFG_NETWORK_CONFIG_H 