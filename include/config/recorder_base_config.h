/*******************************************************************
 * File          : recorder_base_config.h
 * Author        : AI Assistant
 * Created       : 2024-12-26
 * Last modified : 2024-12-26
 *------------------------------------------------------------------
 * Description :
 * 录音基站配置模块头文件 - 基于旧项目0mini.c和0recorder.c的真实结构
 * 支持录音模块和最小基站两种设备类型，使用相同的数据结构
 *------------------------------------------------------------------
 * Modification history :
 * 2024-12-26 : 基于旧项目实际结构重新创建
 *******************************************************************/

#ifndef __RECORDER_BASE_CONFIG_H__
#define __RECORDER_BASE_CONFIG_H__

#include <stdint.h>
#include <stdio.h>

// 设备类型枚举 - 基于旧项目0define.h
typedef enum {
    DEVICE_TYPE_RECORDER = 0x13,    // 录音模块 (eTypeRecorder)
    DEVICE_TYPE_MINI = 0x17         // 最小基站 (eTypeMini)
} recorder_base_device_type_t;

// 基于旧项目1rwethconfig.h的网络配置结构
typedef struct {
    char ip[16];          // IP地址字符串
    char mask[16];        // 子网掩码
    char gateway[16];     // 网关地址
    char dns[16];         // DNS服务器
    char mac[18];         // MAC地址
} st_net_config_t;

typedef struct {
    FILE *fpeth;
    st_net_config_t ethconfig;
    int wlan_enable_val;
} st_board_net_t;

// 基于旧项目0define.h的二进制网络配置结构
typedef struct {
    uint32_t ip;          // IP地址（网络字节序）
    uint32_t mask;        // 子网掩码
    uint32_t gateway;     // 网关
    uint32_t dns;         // DNS服务器
    uint8_t mac[6];       // MAC地址（6字节）
} __attribute__((packed)) st_cfg_net_t;

// 基于旧项目1rwcommon.h的通用配置结构
typedef struct {
    uint8_t ds;           // 调度
    uint8_t sw;           // 交换机
    uint8_t conf_num;     // 会议数
    uint8_t normal_num;   // 常态数
} __attribute__((packed)) st_cfg_common_t;

// 基于旧项目0define.h的板卡基础配置结构
typedef struct {
    uint32_t daemon_ip;              // 守护进程IP
    uint16_t daemon_port;            // 守护进程端口
    uint32_t log_ip;                 // 日志IP
    uint16_t log_port;               // 日志端口
    uint32_t cfg_ip;                 // 配置IP
    uint16_t cfg_port;               // 配置端口
    uint8_t log_level;               // 日志级别
    uint8_t log_to_where;            // 日志输出位置
    uint16_t data_listen_port;       // 数据监听端口
    uint16_t data_send_port;         // 数据发送端口
} __attribute__((packed)) st_cfg_board_basic_t;

// 基于旧项目1rwconference.h的会议配置结构
typedef struct {
    uint32_t work_mode;       // 工作模式（只读）
    uint16_t spec_function;   // 特殊功能
    uint8_t peer_base_num;    // 对端基站数
    uint32_t voice_ip;        // 语音IP
    uint16_t vbus_base_port;  // 语音总线基础端口
    uint8_t vchan_number;     // 语音通道数
    uint16_t buffertime;      // 缓冲时间
    uint16_t downtime;        // 掉线时间
} __attribute__((packed)) st_cfg_conf_t;

// 基于旧项目1rwconference.h的对端基站配置结构
typedef struct {
    uint32_t peer_ip;                   // 对端IP
    uint32_t peer_voice_ip;             // 对端语音IP
    uint16_t peer_data_listen_port;     // 对端数据监听端口
    uint16_t peer_voice_port_base;      // 对端语音端口基址
    uint32_t peer_net_address:24;       // 对端网络地址（24位）
    uint8_t peer_type;                  // 对端类型（关键差异点）
    uint8_t peer_vbus_to_chan[12];      // 语音总线到通道映射
} __attribute__((packed)) st_cfg_peer_base_t;

// 录音基站配置状态结构
typedef struct {
    recorder_base_device_type_t device_type;  // 设备类型
    int is_configured;                        // 是否已配置
    char last_error[256];                     // 最后错误信息
} recorder_base_status_t;

// 统一的录音基站配置结构
typedef struct {
    st_board_net_t board_net;              // 网络板卡配置
    st_cfg_net_t net_config;               // 网络配置
    st_cfg_common_t common_config;         // 通用配置
    st_cfg_board_basic_t board_basic;      // 板卡基础配置
    st_cfg_conf_t conference_config;       // 会议配置
    st_cfg_peer_base_t peer_configs[8];    // 对端配置数组（最大8个）
    recorder_base_device_type_t device_type; // 设备类型
} recorder_base_config_t;

// JSON格式的配置结构（用于API交互）
typedef struct {
    // 网络配置
    char ip[16];
    char mask[16];
    char gateway[16];
    char dns[16];
    char mac[18];
    
    // 通用配置
    uint8_t ds;                     // 调度
    uint8_t sw;                     // 交换机
    uint8_t conf_num;               // 会议数
    uint8_t normal_num;             // 常态数
    
    // 板卡基础配置
    char daemon_ip[16];             // 守护进程IP
    uint16_t daemon_port;           // 守护进程端口
    char log_ip[16];                // 日志IP
    uint16_t log_port;              // 日志端口
    char cfg_ip[16];                // 配置IP
    uint16_t cfg_port;              // 配置端口
    uint8_t log_level;              // 日志级别
    uint8_t log_to_where;           // 日志输出位置
    uint16_t data_listen_port;      // 数据监听端口
    uint16_t data_send_port;        // 数据发送端口
    
    // 会议配置
    uint32_t work_mode;             // 工作模式
    uint16_t spec_function;         // 特殊功能
    uint8_t peer_base_num;          // 对端基站数
    char voice_ip[16];              // 语音IP
    uint16_t vbus_base_port;        // 语音总线基础端口
    uint8_t vchan_number;           // 语音通道数
    uint16_t buffertime;            // 缓冲时间
    uint16_t downtime;              // 掉线时间
    
    // 对端配置数组
    struct {
        char peer_ip[16];               // 对端IP
        char peer_voice_ip[16];         // 对端语音IP
        uint16_t peer_data_listen_port; // 对端数据监听端口
        uint16_t peer_voice_port_base;  // 对端语音端口基址
        uint32_t peer_net_address;      // 对端网络地址（24位）
        uint8_t peer_type;              // 对端类型
        uint8_t peer_vbus_to_chan[12];  // 语音总线到通道映射
    } peer_configs[8];
    
    // 设备信息
    uint8_t device_type;            // 设备类型：0x13(录音) 或 0x17(最小基站)
    char device_name[32];           // 设备名称
} recorder_base_config_json_t;

// 核心函数声明

/**
 * 读取录音基站配置 - 基于旧项目mini_read_cfg函数
 * @param config 配置结构指针
 * @param device_type 设备类型
 * @return 0成功，其他值失败
 */
int read_recorder_base_config(recorder_base_config_t *config, recorder_base_device_type_t device_type);

/**
 * 写入录音基站配置 - 基于旧项目mini_write_cfg函数
 * @param config 配置结构指针
 * @param device_type 设备类型
 * @return 0成功，其他值失败
 */
int write_recorder_base_config(const recorder_base_config_t *config, recorder_base_device_type_t device_type);

/**
 * 验证录音基站配置
 * @param config JSON配置结构指针
 * @return 0有效，-1无效
 */
int validate_recorder_base_config(const recorder_base_config_json_t *config);

/**
 * JSON配置转换为二进制配置
 * @param json_config JSON配置
 * @param binary_config 二进制配置
 * @return 0成功，-1失败
 */
int convert_recorder_base_json_to_binary(const recorder_base_config_json_t *json_config,
                                       recorder_base_config_t *binary_config);

/**
 * 二进制配置转换为JSON配置
 * @param binary_config 二进制配置
 * @param json_config JSON配置
 * @return 0成功，-1失败
 */
int convert_recorder_base_binary_to_json(const recorder_base_config_t *binary_config,
                                       recorder_base_config_json_t *json_config);

/**
 * 设置默认配置
 * @param config 配置结构指针
 * @param device_type 设备类型
 */
void set_default_recorder_base_config(recorder_base_config_t *config, recorder_base_device_type_t device_type);

/**
 * 检测设备类型
 * @return 设备类型
 */
recorder_base_device_type_t detect_recorder_base_device_type(void);

/**
 * 获取设备类型名称
 * @param device_type 设备类型
 * @return 设备名称字符串
 */
const char* get_recorder_base_device_name(recorder_base_device_type_t device_type);

#endif /* __RECORDER_BASE_CONFIG_H__ */ 