#ifndef WEBCFG_CONFIG_MANAGER_H
#define WEBCFG_CONFIG_MANAGER_H

#include <cJSON.h>
#include <stdint.h>

// 配置类型枚举
typedef enum {
    CONFIG_TYPE_NETWORK = 0,    // 网络配置
    CONFIG_TYPE_CENTER,         // 呼叫中心配置  
    CONFIG_TYPE_GATEWAY,        // 网关配置
    CONFIG_TYPE_RECORDER,       // 录音机配置
    CONFIG_TYPE_MINI,          // 迷你模块配置
    CONFIG_TYPE_SWITCH,        // 交换机配置
    CONFIG_TYPE_SCI,           // 基站配置
    CONFIG_TYPE_SYSTEM,        // 系统配置
    CONFIG_TYPE_MAX
} config_type_t;

// 配置验证函数类型
typedef int (*config_validator_func_t)(const cJSON *data);

// 配置转换函数类型  
typedef int (*config_converter_func_t)(const cJSON *json_data, void *binary_data);

// 配置映射结构
typedef struct {
    config_type_t type;         // 配置类型
    char *name;                 // 配置名称
    char *file_path;           // 配置文件路径
    size_t data_size;          // 数据大小
    config_validator_func_t validator;    // 验证函数
    config_converter_func_t json_to_binary; // JSON转二进制
    config_converter_func_t binary_to_json; // 二进制转JSON
} config_mapping_t;

/**
 * 初始化配置管理器
 * @return 0成功，-1失败
 */
int config_manager_init(void);

/**
 * 销毁配置管理器
 */
void config_manager_destroy(void);

/**
 * 注册配置映射
 * @param mapping 配置映射结构
 * @return 0成功，-1失败
 */
int config_manager_register(const config_mapping_t *mapping);

/**
 * 读取配置数据
 * @param type 配置类型
 * @param json_data 输出JSON数据
 * @return 0成功，-1失败
 */
int config_manager_read(config_type_t type, cJSON **json_data);

/**
 * 保存配置数据
 * @param type 配置类型
 * @param json_data 输入JSON数据
 * @return 0成功，-1失败
 */
int config_manager_save(config_type_t type, const cJSON *json_data);

/**
 * 验证配置数据
 * @param type 配置类型
 * @param json_data JSON数据
 * @return 0成功，-1失败
 */
int config_manager_validate(config_type_t type, const cJSON *json_data);

/**
 * 获取配置映射
 * @param type 配置类型
 * @return 配置映射指针，NULL表示未找到
 */
const config_mapping_t* config_manager_get_mapping(config_type_t type);

#endif // WEBCFG_CONFIG_MANAGER_H 