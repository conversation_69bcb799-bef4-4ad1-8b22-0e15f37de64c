#ifndef INCLUDE_CONFIG_CENTER_CENTER_CONFIG_H
#define INCLUDE_CONFIG_CENTER_CENTER_CONFIG_H

/**
 * @file center_config.h
 * @brief 呼叫中心配置模块公共头文件
 * <AUTHOR> Assistant
 * @date 2024-12-26
 */

// 包含具体的实现头文件
#include "../../../src/config/center/center_config.h"

// API处理函数声明
int center_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data);
int center_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data);
int center_handle_config_request(struct MHD_Connection *connection, const char *method, 
                                const char *url, const char *request_data);

#endif /* INCLUDE_CONFIG_CENTER_CENTER_CONFIG_H */ 