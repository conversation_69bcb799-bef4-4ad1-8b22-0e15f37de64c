/**
 * @file config_base.h
 * @brief 配置操作基础接口定义
 * <AUTHOR> Team
 * @date 2024
 */

#ifndef CONFIG_BASE_H
#define CONFIG_BASE_H

#include <stdint.h>
#include <stddef.h>

/**
 * 配置操作统一接口结构体
 * 用于实现多态化的配置操作
 */
typedef struct {
    /**
     * 读取配置
     * @param config 配置数据指针
     * @return int 错误码，0表示成功
     */
    int (*read)(void* config);
    
    /**
     * 写入配置  
     * @param config 配置数据指针
     * @return int 错误码，0表示成功
     */
    int (*write)(const void* config);
    
    /**
     * 验证配置
     * @param config 配置数据指针
     * @return int 错误码，0表示成功
     */
    int (*validate)(const void* config);
    
    /**
     * 转换为JSON格式
     * @param config 配置数据指针
     * @return char* JSON字符串，需要调用者释放
     */
    char* (*to_json)(const void* config);
    
    /**
     * 从JSON解析配置
     * @param json_str JSON字符串
     * @param config 配置数据指针  
     * @return int 错误码，0表示成功
     */
    int (*from_json)(const char* json_str, void* config);
    
    /**
     * 设置默认值
     * @param config 配置数据指针
     */
    void (*set_defaults)(void* config);
    
    /**
     * 获取配置大小
     * @return size_t 配置结构体大小
     */
    size_t (*get_size)(void);
    
    /**
     * 获取配置类型名称
     * @return const char* 类型名称字符串
     */
    const char* (*get_type_name)(void);
    
} config_operations_t;

#endif /* CONFIG_BASE_H */ 