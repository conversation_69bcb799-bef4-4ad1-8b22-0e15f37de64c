#ifndef INCLUDE_CONFIG_STATION_STATION_CONFIG_H
#define INCLUDE_CONFIG_STATION_STATION_CONFIG_H

/**
 * @file station_config.h
 * @brief 基站配置模块公共头文件
 * <AUTHOR> Assistant
 * @date 2024-12-26
 */

// 包含具体的实现头文件
#include "../../../src/config/station/station_config.h"

// API处理函数声明
int station_handle_config_get(struct MHD_Connection *connection, const char *url, const char *request_data);
int station_handle_config_post(struct MHD_Connection *connection, const char *url, const char *request_data);
int station_handle_device_type_get(struct MHD_Connection *connection, const char *url, const char *request_data);
int station_handle_config_request(struct MHD_Connection *connection, const char *method, 
                                 const char *url, const char *request_data);

#endif /* INCLUDE_CONFIG_STATION_STATION_CONFIG_H */ 