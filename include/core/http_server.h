#ifndef WEBCFG_HTTP_SERVER_H
#define WEBCFG_HTTP_SERVER_H

#include <microhttpd.h>
#include <cJSON.h>
#include <stdint.h>

// HTTP状态码定义
#define HTTP_OK                200
#define HTTP_BAD_REQUEST       400
#define HTTP_UNAUTHORIZED      401
#define HTTP_FORBIDDEN         403
#define HTTP_NOT_FOUND         404
#define HTTP_INTERNAL_ERROR    500

// 服务器配置结构
typedef struct {
    uint16_t port;              // 监听端口
    char *web_root;             // 网页根目录
    int max_connections;        // 最大连接数
    int timeout;                // 超时时间(秒)
} http_server_config_t;

// HTTP服务器结构
typedef struct {
    struct MHD_Daemon *daemon;  // MHD守护进程
    http_server_config_t config; // 服务器配置
    int running;                // 运行状态
} http_server_t;

/**
 * 初始化HTTP服务器
 * @param config 服务器配置
 * @return 服务器实例指针，失败返回NULL
 */
http_server_t* http_server_init(const http_server_config_t *config);

/**
 * 启动HTTP服务器
 * @param server 服务器实例
 * @return 0成功，-1失败
 */
int http_server_start(http_server_t *server);

/**
 * 停止HTTP服务器
 * @param server 服务器实例
 */
void http_server_stop(http_server_t *server);

/**
 * 销毁HTTP服务器
 * @param server 服务器实例
 */
void http_server_destroy(http_server_t *server);

/**
 * HTTP请求处理回调函数类型
 */
typedef int (*http_handler_func_t)(struct MHD_Connection *connection,
                                   const char *url,
                                   const char *method,
                                   const char *upload_data,
                                   size_t *upload_data_size,
                                   void **con_cls);

#endif // WEBCFG_HTTP_SERVER_H 