#ifndef WEBCFG_RESPONSE_H
#define WEBCFG_RESPONSE_H

#include <microhttpd.h>
#include <cJSON.h>

// API响应结构
typedef struct {
    int code;                  // 状态码
    char *message;             // 消息
    cJSON *data;              // 数据
    long timestamp;           // 时间戳
} api_response_t;

/**
 * 创建API响应
 * @param code 状态码
 * @param message 消息
 * @param data 数据JSON对象
 * @return 响应结构指针
 */
api_response_t* response_create(int code, const char *message, cJSON *data);

/**
 * 发送JSON响应
 * @param connection MHD连接
 * @param response 响应结构
 * @return MHD处理结果
 */
int response_send_json(struct MHD_Connection *connection, 
                      const api_response_t *response);

/**
 * 发送成功响应
 * @param connection MHD连接
 * @param data 数据JSON对象
 * @return MHD处理结果
 */
int response_send_success(struct MHD_Connection *connection, cJSON *data);

/**
 * 发送错误响应
 * @param connection MHD连接
 * @param code 错误码
 * @param message 错误消息
 * @return MHD处理结果
 */
int response_send_error(struct MHD_Connection *connection, 
                       int code, const char *message);

/**
 * 发送文件响应
 * @param connection MHD连接
 * @param filepath 文件路径
 * @param content_type MIME类型
 * @return MHD处理结果
 */
int response_send_file(struct MHD_Connection *connection,
                      const char *filepath,
                      const char *content_type);

/**
 * 销毁响应结构
 * @param response 响应结构
 */
void response_destroy(api_response_t *response);

/**
 * 获取MIME类型
 * @param filepath 文件路径
 * @return MIME类型字符串
 */
const char* response_get_mime_type(const char *filepath);

#endif // WEBCFG_RESPONSE_H 