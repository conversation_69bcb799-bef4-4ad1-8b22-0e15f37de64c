#ifndef WEBCFG_ROUTER_H
#define WEBCFG_ROUTER_H

#include <microhttpd.h>
#include <cJSON.h>

// 路由处理函数类型
typedef int (*route_handler_func_t)(struct MHD_Connection *connection,
                                   const char *url,
                                   cJSON *request_data);

// API路由结构
typedef struct {
    char *method;               // HTTP方法 (GET/POST/PUT/DELETE)
    char *path;                // API路径
    route_handler_func_t handler; // 处理函数
    int auth_required;         // 是否需要认证
} api_route_t;

// 路由器结构
typedef struct {
    api_route_t *routes;       // 路由表
    int route_count;           // 路由数量
    int capacity;              // 路由表容量
} router_t;

/**
 * 初始化路由器
 * @return 路由器实例指针，失败返回NULL
 */
router_t* router_init(void);

/**
 * 注册路由
 * @param router 路由器实例
 * @param method HTTP方法
 * @param path 路径
 * @param handler 处理函数
 * @param auth_required 是否需要认证
 * @return 0成功，-1失败
 */
int router_register(router_t *router,
                   const char *method,
                   const char *path,
                   route_handler_func_t handler,
                   int auth_required);

/**
 * 处理HTTP请求
 * @param router 路由器实例
 * @param connection MHD连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接类指针
 * @return MHD处理结果
 */
int router_handle_request(router_t *router,
                         struct MHD_Connection *connection,
                         const char *url,
                         const char *method,
                         const char *upload_data,
                         size_t *upload_data_size,
                         void **con_cls);

/**
 * 销毁路由器
 * @param router 路由器实例
 */
void router_destroy(router_t *router);

/**
 * 路径匹配函数
 * @param pattern 路径模式 (支持*通配符)
 * @param url 实际URL
 * @return 1匹配，0不匹配
 */
int router_match_path(const char *pattern, const char *url);

#endif // WEBCFG_ROUTER_H 